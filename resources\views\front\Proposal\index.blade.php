@extends('front.layout.layout')

@section('body')

 @include('include.alert')

   <div class="container">
    @vite('resources/js/app.js')



        <div class="content-wrapper">
            <div>
                <!-- بطاقة معلومات المستقل -->
                <div class="card">
                    <h2 class="card-title"><i class="fas fa-user-tie"></i> معلومات المستقل</h2>

                    <div class="freelancer-info">
                        <div class="freelancer-avatar"><img src="{{asset($Proposal->avater)}}" alt="e"></div>
                        <div class="freelancer-details">
                            <h3 class="freelancer-name"> {{$Proposal->user_name}}</h3>
                            <div class="freelancer-title">  {{$Proposal->cat_name}}</div>
                            <div class="freelancer-rating">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                                <span>4.5 (32 تقييم)</span>
                            </div>
                            @if ($Proposal->identity_verification == 1)

                            <div class="verification-badge">
                                <i class="fas fa-check-circle"></i> حساب موثوق
                            </div>
                            @else
                            <div class="verification-badge verification-badge-an">
                                <i class="fas fa-times-circle"></i>حساب غير موثوق
                            </div>

                            @endif
                        </div>
                    </div>

                    <div class="freelancer-meta">
                        <div class="meta-item">
                            <i class="fas fa-briefcase"></i>
                            <span>معرض الاعمال : {{$Proposal->project_count}}</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span> {{$Proposal->address}}</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            <span>مستقل منذ: {{\Carbon\Carbon::parse($Proposal->user_created_at)->format('Y-m')}}</span>
                        </div>

                    </div>
                </div>

                <!-- بطاقة تفاصيل العرض -->
                <div class="card">
                    <h2 class="card-title"><i class="fas fa-file-alt"></i> تفاصيل العرض</h2>

                    <div class="proposal-details">
                        <div class="detail-item">
                            <span class="detail-label">السعر المطلوب</span>
                            <span class="detail-value price">{{$Proposal->amount}} </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">مدة التنفيذ</span>
                            <span class="detail-value">{{$Proposal->DeliveryDate}} أيام</span>
                        </div>
                         <div class="detail-item">
                            <span class="detail-label">الحالة </span>
                            @if ($Proposal->st == 1)
                            <span class="detail-value">مفتوح</span>
                            @elseif ($Proposal->st == 0)
                            <span class="detail-value">مغلق </span>
                            @else
                            <span class="detail-value">قيد التنفيذ  </span>
                            @endif
                        </div>
                        {{--
                        <div class="detail-item">
                            <span class="detail-label">نوع التسليم</span>
                            <span class="detail-value">تدريجي</span>
                        </div> --}}
                        <div id="app">
                        <div class="detail-item">
                            <span class="detail-label">تاريخ النشر </span>
                                                 {{-- <time-ago time="{{ $work->created_at->toIso8601String() }}"></time-ago> --}}
                      <time-ago time="{{$Proposal->created_at->toIso8601String() }}"></time-ago>
                            {{-- <span class="detail-value">{{$Proposal->created_at}}</span> --}}
                        </div>
                        </div>

                    </div>

                    <div class="proposal-description">
                        <p>{{$Proposal->message}}</p>
                    </div>
                </div>
            </div>

            <div>

                <!-- بطاقة الإجراءات -->
                <div class="card actions-card">
                    <h2 class="card-title"><i class="fas fa-tasks"></i> إجراءات</h2>
                    @if ($Proposal->st == 2)

                    <h2 class="card-title"><i class="fas fa-tasks"></i> المشروع قيد التنفيذ</h2>

                    <a href="{{route('user.message',$Proposal->userID)}}" class="btn btn-message" style="margin-top: 15px;">
                    <i class="fas fa-detils">التفاصيل</i>
                    </a>
                    @endif
                     @if ($Proposal->st == 1)
                    <div class="action-buttons">
                        <form method="POST" action="{{route('user.Proposal_decision.create')}}">
                            @csrf
                            <input style="display: none" type="text" name="UD" value="{{$arrOfId['uID']}}">
                            <input  style="display: none"  type="text" name="WD" value="{{$arrOfId['WID']}}">
                            <input  style="display: none"  type="text" name="PD" value="{{$Proposal->ProposalsID}}">
                            <button class="btn btn-accept">
                                <i class="fas fa-check-circle"></i> قبول العرض
                            </button>
                        </form>
                        <form method="POST" action="{{route('user.Proposal.reject',$proposal->ProposalsID)}}">
                            @csrf
                            <button class="btn btn-reject">
                                <i class="fas fa-times-circle"></i> رفض العرض
                            </button>
                        </form>
                    </div>
                    @endif

                    <a href="{{route('user.message',$Proposal->userID)}}" class="btn btn-message" style="margin-top: 15px;">
                        <i class="fas fa-comments"></i> مراسلة المستقل
                    </a>

                    <div class="action-note">
                        <p><i class="fas fa-info-circle"></i> عند قبول العرض، سيتم خصم المبلغ من رصيدك وسيتم تحويله للمستقل بعد إكمال المشروع بنجاح.</p>
                    </div>

                    <div class="protection-notice">
                        <i class="fas fa-shield-alt"></i>
                        <span>محمي بضمان أداء العملية. ستتمكن من طلب استرداد الأموال إذا لم يتم إكمال العمل وفقًا للمواصفات.</span>
                    </div>
                </div>
            </div>
        </div>
    </div>


    @section('scriptFile')
        <script src="{{asset('front/asset/js/menu-toggle.js')}}"></script>
    @endsection
@endsection

{{-- 'users.identity_verification', --}}




@extends('front.layout.layout')

@section('body')

 @include('include.alert') 
 
      <main class="container">
        <div class="profile-setup-container">
            <div class="profile-setup-header">
                <div class="step-indicator">
                    <div class="step completed">1</div>
                    <div class="step-line"></div>
                    <div class="step active">2</div>
                    <div class="step-line"></div>
                    <div class="step">3</div>
                    <div class="step-line"></div>
                    <div class="step">4</div>
                    <div class="step-line"></div>
                    <div class="step">5</div>
                </div>
                <h2>اختيار صورة الملف الشخصي</h2>
                <p class="step-description">اختر صورة شخصية لتخصيص حسابك. ستظهر الصورة المختارة هنا فورًا.</p>
            </div>
            
            <div class="profile-sections">
                <div class="profile-section-card completed">
                    <div class="section-icon">👤</div>
                    <div class="section-content">
                        <h3>المعلومات الشخصية</h3>
                        <p>تم إكمال المعلومات الأساسية</p>
                    </div>
                    <div class="section-status">✓</div>
                </div>
                
                <div class="profile-section-card active">
                    <div class="section-icon">📷</div>
                    <div class="section-content">
                        <h3>صورة الملف الشخصي</h3>
                        <p>اختر صورة تعبر عن هويتك المهنية</p>
                    </div>
                    <div class="section-status">...</div>
                </div>
                
                <div class="profile-section-card">
                    <div class="section-icon">📌</div>
                    <div class="section-content">
                        <h3>اختر نوع حسابك</h3>
                        <p>    تحديد نوع الحساب الذي ترغب في إنشائه</p>
                    </div>
                    <div class="section-status">></div>
                </div>
                
                <div class="profile-section-card">
                    <div class="section-icon">📌</div>
                    <div class="section-content">
                        <h3>المهارات</h3>
                        <p>أضف مهاراتك الرئيسية لمساعدة العملاء في العثور عليك بسهولة</p>
                    </div>
                    <div class="section-status">></div>
                </div>
                
                <div class="profile-section-card">
                    <div class="section-icon">📌</div>
                    <div class="section-content">
                        <h3>معرض الأعمال</h3>
                        <p>قم بتحميل أعمالك السابقة لعرض خبرتك</p>
                    </div>
                    <div class="section-status">></div>
                </div>
            </div>
            
            <div class="profile-upload">
                <div class="upload-container">
                    <div class="upload-controls">
                        <div class="upload-methods">
                            <label for="photo-upload" class="upload-button primary-upload">
                                <span class="upload-icon">📁</span>
                                <form action="{{route('profile.setup.step3')}}" method="POST" enctype="multipart/form-data">
                                    @csrf
                                اختر صورة من جهازك
                         <input type="file" name="img" id="photo-upload" accept="image/*" hidden required>
                            </label>
                        </div>   
                        <p class="upload-hint">يفضل صورة مربعة بحجم 400×400 بكسل على الأقل</p>
                        
                        <div class="upload-requirements">
                            <h4>متطلبات الصورة:</h4>
                            <ul>
                                <li>يجب أن تكون بصيغة JPG أو PNG</li>
                                <li>الحد الأقصى للحجم: 5 ميجابايت</li>
                                <li>يفضل أن تكون صورة شخصية واضحة</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="upload-progress" id="upload-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-text" id="progress-text">جاري التحميل... 0%</div>
                </div>
                
                <div class="upload-actions">
                    <button id="save-photo" class="secondary-button" >حفظ الصورة والمتابعة</button>
                    <a href="{{route('user.account-type')}}" id="skip-button" class="secondary-button">تخطي هذه الخطوة</a>
                 </form>

                </div>
            </div>
        </div>
    </main>




    @section('scriptFile')
        <script src="{{asset('front/asset/js/upload-photo.js')}}"></script>
    @endsection
@endsection







<?php

namespace App\Http\Controllers\back;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Post;
use Illuminate\Http\Request;

use App\Helpers\ApiHelper;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{
    
    public function login2(Request $request){


     $user = User::select()->first();
      
      return ApiHelper::apiResponse(true, 'تم بنجاح', ['user' => 'Mohammed']);


      // $token = $user->createToken($user->name);
 
      //  return ['token' => $token->plainTextToken];
    }

     
public function login(Request $request)
{
    $request->validate([
        'email'    => 'required|email',
        'password' => 'required|string',
    ]);

    $user = User::where('email', $request->email)->first();

    if (!$user || !Hash::check($request->password, $user->password)) {
        return ApiHelper::apiResponse(false, 'البريد الإلكتروني أو كلمة المرور غير صحيحة', null, 401);
    }

    // ✅ حذف كل التوكنات القديمة لهذا المستخدم
    $user->tokens()->delete();

    // ✅ إنشاء توكن جديد بعد التأكد
    $token = $user->createToken('auth_token')->plainTextToken;

    return ApiHelper::apiResponse(true, 'تم تسجيل الدخول بنجاح', [
        'user'  => $user,
        'token' => $token,
    ]);
}



    public function signup(Request $request)
{
    $request->validate([
        'name'     => 'required|string|max:255',
        'email'    => 'required|email|unique:users,email',
        'password' => 'required|string|min:6|confirmed', // لازم تبعت password_confirmation
    ]);

    $user = User::create([
        'name'     => $request->name,
        'email'    => $request->email,
        'password' => bcrypt($request->password),
    ]);

    $token = $user->createToken('auth_token')->plainTextToken;

    return ApiHelper::apiResponse(true, 'تم إنشاء الحساب بنجاح', [
        'user'  => $user,
        'token' => $token,
    ]);
}



    public function deletePost(Request $request, $id)
    {
        $user = $request->user();
        $post = Post::find($id);

        if (!$post) {
            return ApiHelper::apiResponse(false, 'المنشور غير موجود', null, 404);
        }

        if ($post->user_id !== $user->id) {
            return ApiHelper::apiResponse(false, 'غير مصرح لك بحذف هذا المنشور', null, 403);
        }

        $post->delete();

        return ApiHelper::apiResponse(true, 'تم حذف المنشور بنجاح', null);
    }
}



        // Toggle menu for mobile
        const toggleBtn = document.querySelector('.menu-toggle');
        const nav = document.querySelector('.main-nav');

        toggleBtn.addEventListener('click', () => {
            nav.classList.toggle('active');
        });
        
        // Tab switching
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                tabButtons.forEach(btn => btn.classList.remove('active'));
                
                // Add active class to clicked button
                button.classList.add('active');
                
                // Hide all tab contents
                tabContents.forEach(content => content.classList.add('hidden'));
                
                // Show the selected tab content
                const tabId = button.getAttribute('data-tab');
                document.getElementById(`${tabId}-tab`).classList.remove('hidden');
            });
        });
        
        // Profile data - This would normally come from a server
        document.addEventListener('DOMContentLoaded', function() {
            // For demo purposes, we'll use localStorage to simulate user data
            // In a real application, this would be fetched from a server
            
            // Check if we have profile data in localStorage
        
            
            // Set verification badge visibility
            const verificationBadge = document.getElementById('verification-badge');
            if (!profileData.verified) {
                verificationBadge.style.display = 'none';
            }
        });
        
        // Profile image upload
        document.getElementById('change-photo').addEventListener('click', function() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            
            input.onchange = function(e) {
                const file = e.target.files[0];
                
                if (file) {
                    const reader = new FileReader();
                    
                    reader.onload = function(event) {
                        document.getElementById('profile-image').src = event.target.result;
                        // In a real app, you would upload this to a server
                        localStorage.setItem('profileImage', event.target.result);
                    };
                    
                    reader.readAsDataURL(file);
                }
            };
            
            input.click();
        });
        
        // Load saved profile image if it exists
        window.addEventListener('load', function() {
            const profileImage = localStorage.getItem('profileImage');
            
            if (profileImage) {
                document.getElementById('profile-image').src = profileImage;
            }
        });
        
        // Portfolio functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Portfolio data
            const portfolioData = [
                {
                    id: 1,
                    title: 'تصميم موقع إلكتروني',
                    description: 'تصميم وتطوير موقع إلكتروني لشركة تجارية متخصصة في بيع المنتجات الإلكترونية. تم استخدام HTML, CSS, JavaScript و PHP.',
                    image: '../../asset/front/images/portfolio-1.jpg',
                    date: '15 يناير 2023',
                    client: 'شركة التقنية الحديثة',
                    category: 'تصميم وتطوير مواقع'
                },
                {
                    id: 2,
                    title: 'تصميم هوية بصرية',
                    description: 'تصميم شعار وهوية بصرية متكاملة لمطعم متخصص في المأكولات البحرية. تضمن المشروع تصميم الشعار، بطاقات العمل، القوائم، واللافتات.',
                    image: '../../asset/front/images/portfolio-2.jpg',
                    date: '3 مارس 2023',
                    client: 'مطعم البحر الأزرق',
                    category: 'تصميم جرافيك'
                },
                {
                    id: 3,
                    title: 'تطبيق جوال',
                    description: 'تصميم وتطوير تطبيق جوال لخدمة توصيل الطلبات للمنازل. تم تطوير التطبيق باستخدام React Native للعمل على أنظمة Android و iOS.',
                    image: '../../asset/front/images/portfolio-3.jpg',
                    date: '20 مايو 2023',
                    client: 'شركة توصيل السريع',
                    category: 'تطوير تطبيقات'
                }
            ];
            
            // Portfolio modal elements
            const modal = document.getElementById('portfolio-modal');
            const modalImage = document.getElementById('modal-image');
            const modalTitle = document.getElementById('modal-title');
            const modalDescription = document.getElementById('modal-description');
            const modalDate = document.getElementById('modal-date');
            const modalClient = document.getElementById('modal-client');
            const modalCategory = document.getElementById('modal-category');
            const closeModal = document.querySelector('.close-modal');
            
            // View portfolio item
            const viewButtons = document.querySelectorAll('.view-portfolio-btn');
            viewButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = parseInt(this.getAttribute('data-id'));
                    const item = portfolioData.find(item => item.id === itemId);
                    
                    if (item) {
                        modalImage.src = item.image;
                        modalImage.alt = item.title;
                        modalTitle.textContent = item.title;
                        modalDescription.textContent = item.description;
                        modalDate.textContent = item.date;
                        modalClient.textContent = item.client;
                        modalCategory.textContent = item.category;
                        
                        modal.style.display = 'block';
                    }
                });
            });
            
            // Close modal
            if (closeModal) {
                closeModal.addEventListener('click', function() {
                    modal.style.display = 'none';
                });
            }
            
            // Close modal when clicking outside
            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
            
            // Edit portfolio item
            const editButtons = document.querySelectorAll('.edit-portfolio-btn');
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = parseInt(this.getAttribute('data-id'));
                    alert('تعديل العنصر رقم ' + itemId + ' (سيتم إضافة هذه الوظيفة قريبًا)');
                });
            });
            
            // Delete portfolio item
            const deleteButtons = document.querySelectorAll('.delete-portfolio-btn');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = parseInt(this.getAttribute('data-id'));
                    if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
                        // Find the portfolio item element
                        const itemElement = this.closest('.portfolio-item');
                        if (itemElement) {
                            itemElement.remove();
                        }
                    }
                });
            });
            
            // Add new portfolio item
            const addPortfolioBtn = document.getElementById('add-portfolio-item');
            if (addPortfolioBtn) {
                addPortfolioBtn.addEventListener('click', function() {
                    alert('سيتم إضافة هذه الوظيفة قريبًا');
                });
            }
        });
<template>
  <div>
          <div class="form-group">
            <label for="department">اختر القسم</label>
                <select v-model="job_id" @change="getJobs" id="department" name="department_id" required>
                     <option value="0">اختر القسم</option>
                     <option v-for="categories in categorie" :key="categories.id" :value="categories.id">{{  categories.name}}</option>
                </select>
            </div>

            <div class="form-group">
                <label for="job">اختر الوظيفة</label>
                 <select  name="job_id"  required>
                    <option v-for="categories in jobs" :key="categories.id" :value="categories.id">{{  categories.name}}</option>
                </select> 
            </div>
  
  </div>
</template>

<script>
export default {
 
  data() {
    return {
    categorie : [], 
    job_id : 0,
    jobs :[]

    };
  },    
  methods:{
    getJobs () {
          fetch('http://127.0.0.1:8000/get-jobs/' +this.job_id)
        .then(res => res.json())
        .then(data => this.jobs = data) 
        .catch(err =>console.log(err.message));
       
    }
  },
 mounted (){
    fetch('http://127.0.0.1:8000/categorie')
        .then(res => res.json())
        .then(data => this.categorie = data) 
        .catch(err =>console.log(err.message));
  },

}
</script>

<style >
.skill-item {
    display: inline-block;
    background-color: #eee;
    padding: 5px 10px;
    margin: 2px;
    border-radius: 4px;
}
</style>
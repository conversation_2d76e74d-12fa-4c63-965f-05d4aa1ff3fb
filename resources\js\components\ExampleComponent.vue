<template>
  <div>
    <h1>Hello from Vue!</h1>
    <button @click="test">Clicked me {{ count }} times</button>

    <div>

       <select v-model="datapost" name="" id="" @change="test" >
         <option v-for="posts in post" :key="posts.id" :value="posts.id">{{  posts.id}}</option>
      </select> 

      <span>{{ last }}</span>
      <!-- {{ posts.name }} -->

  </div>
  </div>


</template>

<script>
export default {
  data() {
    return {
      count: 0,
      post : ['test'],
      onepost :'f',
      datapost :1,
      last :''
    };
  },
  mounted (){
    fetch('http://127.0.0.1:8000/post')
        .then(res => res.json())
        .then(data => this.post = data) 
        .catch(err =>console.log(err.message));
  },
  methods:{
    test () {
          fetch('http://127.0.0.1:8000/get-jobs/1' +this.datapost)
        .then(res => res.json())
        .then(data => this.last = data) 
        .catch(err =>console.log(err.message));
       
    }
  }
};
</script>




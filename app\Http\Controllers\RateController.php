<?php

namespace App\Http\Controllers;

use App\Models\front\Proposal;
use App\Models\front\Work;
use App\Models\Proposal_decision;
use App\Models\Rate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RateController extends Controller
{
    //

        public function index(Request $request ) {

       $ID = [
            'work_id'=> $request->work_id,
            'freelance_id'=> $request->freelance_id,
            'decision'=> $request->decision,
        ];

        return view("front.rate.index",compact("ID"));
    }

    public function create(Request $request) {


        $chack = Rate::where("decisions",$request->decision)->first();

        if($chack) {
            return redirect()->back()->with("error","تم التقيم مسبقا");
        }

      $decisions = Proposal_decision::where("id",$request->decision)->first();

      $work = Work::where("id",$request->work_id)->first();

      if($decisions) {
         $decisions ->update([
            "status"=> 1,
         ]);
      }else{
            return redirect()->back()->with("error","حدث خطاء ما");

      }
      if($work) {

         Proposal::where('work_id', $work->id)
        // ->where('status', 1)
        ->update(['status' => 3]);

         $work ->update([
            "status"=> 3,
         ]);
      }else{
            return redirect()->back()->with("error","حدث خطاء ما");

      }


        $rate = $request->rating;

        if($request->rating == null) {
             $rate  = 1;
        }

        $rate = Rate::create([
            'user_id' =>Auth::user()->id,
            'rating'=> $rate,
            'freelance_id'=>$request->freelance_id,
            'work_id'=>$request->work_id,
            'decisions'=>$request->decision,
            'comment'=>$request->comment
        ]);
        return redirect()->route('user.userDashboard')->with('success','تم اضافة تقيمك');

    }
}

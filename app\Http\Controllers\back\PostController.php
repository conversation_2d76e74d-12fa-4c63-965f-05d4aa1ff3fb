<?php

namespace App\Http\Controllers\back;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Post;
use App\Helpers\ApiHelper;


class PostController extends Controller
{

public function getAllPosts()
{
    $posts = Post::with('user')->orderBy('id', 'desc')->get();

    return ApiHelper::apiResponse(true, 'جميع المنشورات', $posts);
}

public function getPostById($id)
{
    $post = Post::with('user')->find($id);

    if (!$post) {
        return ApiHelper::apiResponse(false, 'المنشور غير موجود', null, 404);
    }

    return ApiHelper::apiResponse(true, 'تم جلب المنشور بنجاح', $post);
}



public function createPost(Request $request)
{
    $request->validate([
        'name' => 'required|string|max:255',
        'type'  => 'required|string',
    ]);

    // احصل على المستخدم من التوكن
    $user = $request->user();

    // أنشئ المنشور مرتبط بالمستخدم
    $post = $user->posts()->create([
        'name' => $request->name,
        'type'  => $request->type,
    ]);

    return ApiHelper::apiResponse(true, 'تم إنشاء المنشور بنجاح', $post);
}

public function updatePost(Request $request, $id)
{
    $request->validate([
        'name' => 'required|string|max:255',
        'type'  => 'required|string',
    ]);

    $user = $request->user();
    $post = Post::find($id);

    if (!$post) {
        return ApiHelper::apiResponse(false, 'المنشور غير موجود', null, 404);
    }

    // تحقق أن المستخدم يملك المنشور
    if ($post->user_id !== $user->id) {
        return ApiHelper::apiResponse(false, 'غير مصرح لك بتعديل هذا المنشور', null, 403);
    }

    // تحديث البيانات
    $post->update([
        'name' => $request->name,
        'type'  => $request->type,
    ]);

    return ApiHelper::apiResponse(true, 'تم تحديث المنشور بنجاح', $post);
}
public function deletePost(Request $request, $id)
{
    $user = $request->user();
    $post = Post::find($id);

    if (!$post) {
        return ApiHelper::apiResponse(false, 'المنشور غير موجود', null, 404);
    }

    if ($post->user_id !== $user->id) {
        return ApiHelper::apiResponse(false, 'غير مصرح لك بحذف هذا المنشور', null, 403);
    }

    $post->delete();

    return ApiHelper::apiResponse(true, 'تم حذف المنشور بنجاح', null);
}


}

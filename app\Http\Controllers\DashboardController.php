<?php

namespace App\Http\Controllers;

use App\Models\front\Work;
use App\Models\Proposal_decision;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    //
    public function userDashboard()
    {

        $close = Work::where("user_id", Auth::user()->id)

            ->where('status', 2)->count();

        $open = Work::where("user_id", Auth::user()->id)->where('status', 1)->count();

        $complat = Work::where("user_id", Auth::user()->id)->where('status', 3)->count();

        $all = Work::where("user_id", Auth::user()->id)->count();

        $status = [
            "close" => $close,
            "open" => $open,
            "complat" => $complat,
            "all" => $all
        ];


        $openWorks = DB::table('works as w')
            ->join('categories as c', 'c.id', '=', 'w.category_id')
            ->join('users as u', 'u.id', '=', 'w.user_id')
            ->select(
                'w.id',
                'w.name',
                'w.created_at',
                'amount',
                'expiration_date',
                'c.name as category_name',
                'u.name as user_name',
                DB::raw('(SELECT COUNT(*) FROM proposals p WHERE p.work_id = w.id) as proposals_count')
            )

            ->where('w.status', 1)

            ->where("w.user_id", Auth::user()->id)

            ->get();



        $offers = DB::table('proposal_decisions as p')
            ->join('works as w', 'w.id', '=', 'p.work_id')
            ->join('users as u', 'u.id', '=', 'p.freelance_id')
            ->select(

                'u.name as user_name',
                'u.id as user_id',
                'w.name as work_name',
                'w.amount',
                'w.expiration_date',
                'w.description',
                'p.created_at',
                'p.id'
            )
            ->where('w.status', 2)

            ->where("w.user_id", Auth::user()->id)

            ->get();

        $complatWork = DB::table('proposal_decisions as p')
            ->join('works as w', 'w.id', '=', 'p.work_id')
            ->join('users as u', 'u.id', '=', 'p.freelance_id')
            ->leftJoin('rates as r', function ($join) {
                $join->on('r.freelance_id', '=', 'u.id')
                    ->on('r.work_id', '=', 'w.id');
            })
            ->select(
                'u.name as user_name',
                'u.id as user_id',
                'w.name as work_name',
                'w.amount',
                'w.expiration_date',
                'w.description',
                'w.created_at',
                'p.proposal_id',
                'p.updated_at',
                'r.rating' // التقييم
            )
            ->where('p.status', 1)

            ->where("w.user_id", Auth::user()->id)

            ->get();


        return   view("front.dashboard.user", compact('complatWork', 'offers', "status", "openWorks"));
    }


    public function freelance()
    {


        $close = DB::table('works as w')
            ->join('proposals as p', 'p.work_id', '=', 'w.id')
            ->where('p.freelance_id', Auth::id()) // الفريلانسر الحالي
            ->where('w.status', 2)               // قيد التنفيذ
            ->count();


        $open = DB::table('works as w')
            ->join('categories as fc', 'w.category_id', '=', 'fc.id')
            ->where('fc.id', Auth::user()->categorie_id) // القسم الخاص بالمستخدم
            ->where('w.status', 1) // الوظائف المفتوحة
            ->count();


        $complat = DB::table('works as w')
            ->join('proposals as p', 'p.work_id', '=', 'w.id')
            ->where('p.freelance_id', Auth::id())
            ->where('w.status', 3)
            ->count();


        $all = Work::where("user_id", Auth::user()->id)->count();

        $status = [
            "close" => $close,
            "open" => $open,
            "complat" => $complat,
            "all" => $all
        ];

        $openWorks = DB::table('works as w')
            ->join('categories as fc', 'w.category_id', '=', 'fc.id')
            ->join('users as u', 'w.user_id', '=', 'u.id')
            ->select(
                'w.id',
                'w.name',
                'w.description',
                'w.amount',
                'w.place',
                'w.expiration_date',
                'w.created_at',   // ✅ أضفته هنا
                'fc.name as category_name',
                'u.name as user_name',
                DB::raw('(SELECT COUNT(*) FROM proposals p WHERE p.work_id = w.id) as proposals_count'),
                DB::raw("
            (
                CASE
                    WHEN w.created_at >= NOW() - INTERVAL 7 DAY THEN 50
                    WHEN w.created_at >= NOW() - INTERVAL 30 DAY THEN 25
                    ELSE 0
                END
                +
                CASE WHEN fc.id IS NOT NULL THEN 50 ELSE 0 END
            ) AS score
        ")
            )
            ->where('fc.id', Auth::user()->categorie_id)
            ->where('w.status', 1)
            ->orderByDesc('score')
            ->orderByDesc('w.created_at')
            ->distinct()
            ->get();


        $offers = DB::table('works as w')
            ->join('proposals as p', 'p.work_id', '=', 'w.id')
            ->join('users as u', 'w.user_id', '=', 'u.id') // ✅ صاحب الوظيفة
            ->where('p.freelance_id', Auth::id()) // المستخدم الحالي هو الفريلانسر
            ->where('w.status', 2)               // 2 = قيد التنفيذ
            ->select(
                'w.*',
                'p.id as proposal_id',
                'p.status as proposal_status',
                'u.name as client_name' // ✅ اسم صاحب المشروع
            )
            ->get();


        $complatWork = DB::table('works as w')
            ->join('proposals as p', 'p.work_id', '=', 'w.id')      // ربط بالعروض
            ->join('users as u', 'w.user_id', '=', 'u.id')          // ربط صاحب المشروع
            ->leftJoin('rates as r', function ($join) {
                $join->on('r.work_id', '=', 'w.id')
                    ->on('r.freelance_id', '=', 'p.freelance_id'); // تقييم الفريلانسر للعمل
            })
            ->where('p.freelance_id', Auth::id())
            ->where('w.status', 3)                                   // المشاريع المكتملة
            ->select(
                'w.*',
                'p.id as proposal_id',
                'p.status as proposal_status',
                'u.name as client_name',
                'r.rating',                                         // التقييم
                'r.comment'                                         // التعليق
            )
            ->get();

        $myProposals = DB::table('proposals as p')
            ->join('works as w', 'w.id', '=', 'p.work_id')                 // ربط المشروع
            ->join('users as u', 'w.user_id', '=', 'u.id')                 // صاحب المشروع
            ->leftJoin('proposal_decisions as pd', function ($join) {       // ربط جدول القرارات
                $join->on('pd.proposal_id', '=', 'p.id')
                    ->on('pd.freelance_id', '=', 'p.freelance_id');
            })
            ->where('p.freelance_id', Auth::id())                          // الفريلانسر الحالي
            ->select(
                'p.id as proposal_id',
                'p.status as proposal_status',
                'w.name as work_name',
                'w.amount',
                'u.name as client_name',
                'pd.status as decision_status',                             // حالة القرار: قبول / رفض / لم يتم
                'w.status as work_status'
            )
            ->get();


            $proposals = Proposal_decision::where('freelance_id',Auth::user()->id)

            ->join('works','works.id','proposal_decisions.work_id')

            ->select('proposal_decisions.id','name','proposal_decisions.created_at','is_read')

            ->latest('proposal_decisions.created_at')

            ->paginate(5);

            $readms = Proposal_decision::where('freelance_id', Auth::user()->id)
        // ->where('status', 1)
        ->update(['is_read' => 1]);

        return view("front.dashboard.freelance", compact('proposals','myProposals','complatWork', 'offers', "status", "openWorks"));
    }
}

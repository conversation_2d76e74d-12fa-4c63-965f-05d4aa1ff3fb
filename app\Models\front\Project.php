<?php

namespace App\Models\front;

use Illuminate\Database\Eloquent\Model;

class Project extends Model
{
    
  protected $fillable = ['name', 'description', 'image', 'user_id', 'job_id', 'category_id', 'created_at', 'updated_at'];



    public function category()
    {
        return $this->belongsTo(Categorie::class, 'category_id');
    }

    public function job()
    {
        return $this->belongsTo(Job_title::class, 'job_id');
    }
    public function Puser()
    {
        return $this->belongsTo(User::class, 'user_id');
    }


}

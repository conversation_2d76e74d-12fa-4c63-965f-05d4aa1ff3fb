@extends('front.layout.layout')

@section('body')

 @include('include.alert') 

     <main class="container">
        <div class="profile-overview">
            <div class="profile-image-container">
                   <div>
                    <i  class="fas fa-camera-alt fw profile-icon" id="imageUpload"></i>
                </div>
                <img id="imagePreview" class="img"  src="{{asset(Auth::guard()->user()->avater)}}" alt="الصورة الشخصية">
            </div>
            
            <div class="profile-name-container">
                <h4 id="job" style="color: #333; ">  {{Auth::guard()->user()->name}}</h4>
            </div>
            
            <div class="profile-tabs">
                <button class="tab-button active" data-tab="info">تعديل الملف الشخصي</button>
           
            </div>
            
            <div class="tab-content" id="info-tab">
                <div class="profile-info-section">
                    <form action="{{route('user.profile.update')}}" method="POST" enctype="multipart/form-data">
                        @csrf
                    <h3>معلومات الاتصال</h3>
                    <div class="info-item-edit">
                         <div class="info-label"> تعديل الاسم</div>
                        <div class="info-value" id="user-email"><input type="text" name="name" value="{{Auth::guard()->user()->name}}"></div> 
                    </div>
                    {{-- <div class="info-item-edit">
                         <div class="info-label">تعديل البريد الإلكتروني </div>
                        <div class="info-value" id="user-email"><input type="email" name="email" value="{{Auth::guard()->user()->email}}"></div> 
                    </div> --}}
                    <div class="info-item-edit">
                         <div class="info-label">تعديل رقم الهاتف  </div>
                        <div class="info-value" id="user-email"><input type="number" name="phon" value="{{Auth::guard()->user()->phon}}"></div> 
                    </div>
                    <div class="info-item-edit">
                         <div class="info-label">تعديل  العنوان  </div>
                        <div class="info-value" id="user-email"><input type="text" name="address" value="{{Auth::guard()->user()->address}}"></div> 
                    </div>
                    <div class="info-item-edit">
                         <div class="info-label">تعديل عدات سنوات الخبرة   </div>
                        <div class="info-value" id="user-email"><input type="text" name="experince" value="{{Auth::guard()->user()->experince}}"></div> 
                    </div>
                    <div class="info-item-edit">
                        <div class="info-label">تعديل  النبذة الشخصية </div>
                        <div class="info-value" id="user-email"><textarea type="text" name="bio" >{{Auth::guard()->user()->bio}}</textarea></div> 
                    </div>
                      <input type="file" name="img" id="imageInput" accept="image/*" style="display: none;">
                    <div class="profile-actions">
                        <button  class="edit-profile-btn">حفظ  </button>
                    </div>
                    </form>
                </div>
            </div>     
                                     
        </div>
    </main>


    

<script>
  // معاينة الصورة
  const imageUpload = document.getElementById('imageUpload');
  const imageInput = document.getElementById('imageInput');
  const imagePreview = document.getElementById('imagePreview');
  
  imageUpload.addEventListener('click', () => {
    imageInput.click();
  });
  
  imageInput.addEventListener('change', (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        imagePreview.src = event.target.result;
        imagePreview.style.display = 'block';
        imageUpload.style.display = 'none';
      };

      reader.readAsDataURL(file);
    }
  });
  
</script>


    @section('scriptFile')
        <script src="{{asset('front/asset/js/menu-toggle.js')}}"></script>
    @endsection
@endsection

        // Toggle menu for mobile
        const toggleBtn = document.querySelector('.menu-toggle');
        const nav = document.querySelector('.main-nav');

        toggleBtn.addEventListener('click', () => {
            nav.classList.toggle('active');
        });
        
        // Character counter for bio
        const bioTextarea = document.getElementById('bio');
        const bioCount = document.getElementById('bio-count');
        
        bioTextarea.addEventListener('input', function() {
            const count = this.value.length;
            bioCount.textContent = count;
            
            if (count > 500) {
                bioCount.style.color = 'red';
            } else {
                bioCount.style.color = '';
            }
        });
        
        // Form submission
        // document.querySelector('.profile-setup-form').addEventListener('submit', function(e) {
        //     e.preventDefault();
            
        //     // Here you would save the employer profile data
        //     // For demo purposes, we'll just redirect to the dashboard
        //     window.location.href = '../dashboard/employer-dashboard.html';
        // });
        
        // Skip button
        document.getElementById('skip-button').addEventListener('click', function() {
            if (confirm('هل أنت متأكد من تخطي هذه الخطوة؟ يمكنك إكمال ملفك الشخصي لاحقًا من إعدادات الحساب.')) {
                window.location.href = '../dashboard/employer-dashboard.html';
            }
        });
   
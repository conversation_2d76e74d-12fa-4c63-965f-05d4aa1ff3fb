 @extends('front.layout.layout')
 @section('title')
     <title> الملف الشخصي</title>
 @endsection

 @section('body')

     @vite('resources/js/app.js')

     @include('include.alert')

     <main class="container">
         <div class="profile-overview">
             <div class="profile-image-container">
                 @if (Auth::guard()->user()->verified == 1)
                     <div class="profile-activity">
                         <img class="activity" src="{{ asset('assets/imgs/icons/activity-03.png') }}" alt="activity">
                     </div>
                 @endif
                 <img src="{{ asset($user->avater) }}" alt="الصورة الشخصية" id="profile-image">
             </div>

             <div class="profile-name-container">
                 <h2 id="user-name"> {{ $user->name }}</h2>
                 <h4 id="job" style="color: #333; "> صاحب عمل</h4>
             </div>

             <div class="profile-tabs">
                 <button class="tab-button active" data-tab="info">المعلومات الشخصية</button>
                 <button class="tab-button" data-tab="portfolio"> الأعمال</button>
                 <button class="tab-button" data-tab="projects">التقيمات</button>
                 {{-- <button class="tab-button" data-tab="reviews">صندوق الوارد</button> --}}
             </div>

             <div class="tab-content" id="info-tab">
                 <div class="profile-info-section">
                     <h3>معلومات الاتصال</h3>
                     <div class="info-item">
                         <div class="input">
                             <div class="info-label">البريد الإلكتروني</div>
                             <div class="info-value" id="user-email"> {{ $user->email }}</div>
                         </div>
                         <div class="stutes">
                             <span>test</span>
                         </div>
                     </div>
                     <div class="info-item">
                         <div class="input">
                             <div class="info-label">رقم الهاتف</div>
                             <div class="info-value" id="user-phone"> {{ $user->phon }}</div>
                         </div>
                         <div class="stutes">
                             <span></span>
                         </div>
                     </div>
                     <div class="info-item">
                         <div class="input">
                             <div class="info-label">الموقع</div>
                             <div class="info-value" id="user-location"> {{$user->address }}</div>
                         </div>
                     </div>
                     <div class="info-item">
                         <div class="input">
                             <div class="info-label">عدد المشاريع المكتملة</div>
                             <div class="info-value" id="user-location"> 0</div>
                         </div>
                     </div>
                 </div>

                 <div class="profile-info-section">
                     <h3>نبذة عن - {{ $user->name }}</h3>
                     <p class="user-bio" id="user-bio">
                         {{ $user->bio }}
                     </p>
                 </div>
             </div>

             <div class="tab-content hidden" id="portfolio-tab">

                 <div class="" id="portfolio-items">
                     <div class="portfolio-item" style="margin-bottom:20px">
                         {{-- <div class="portfolio-image">

                        </div> --}}
                         @foreach ($works as $work)
                             <div class="portfolio-info" style="border-bottom: 1px solid #33333324;">
                                 <h4>{{ $work->name }} </h4>
                                 <p> {{ $work->description }} </p>
                                 <div class="control">
                                     <a class="btn btn-primary" href="{{ route('user.work.display', $work->id) }}"
                                         style="  background-color: #1877F2 !important;"href="">عرض</a>
                                 </div>
                                 <span>الحاله :</span> مفتوح
                             </div>
                         @endforeach
                     </div>

                 </div>

                 <!-- Portfolio Modal -->
                 <div id="portfolio-modal" class="portfolio-modal">
                     <div class="portfolio-modal-content">
                         <span class="close-modal">&times;</span>
                         <div class="portfolio-modal-body">
                             <div class="portfolio-modal-image">
                                 <img id="modal-image" src="" alt="">
                             </div>
                             <div class="portfolio-modal-details">
                                 <h3 id="modal-title"></h3>
                                 <p id="modal-description"></p>
                                 <div class="portfolio-meta">
                                     <div class="meta-item">
                                         <span class="meta-label">التاريخ:</span>
                                         <span id="modal-date"></span>
                                     </div>
                                     <div class="meta-item">
                                         <span class="meta-label">العميل:</span>
                                         <span id="modal-client"></span>
                                     </div>
                                     <div class="meta-item">
                                         <span class="meta-label">الفئة:</span>
                                         <span id="modal-category"></span>
                                     </div>
                                 </div>
                             </div>
                         </div>
                     </div>
                 </div>
             </div>
         </div>
     </main>


 @section('scriptFile')
     <script src="{{ asset('front/asset/js/profile.js') }}"></script>
 @endsection
@endsection

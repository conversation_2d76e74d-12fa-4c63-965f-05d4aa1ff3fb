import { createApp } from 'vue';

// import ExampleComponent from './components/ExampleComponent.vue';
import app2component from './components/app2Component.vue';
import selestForm from './components/selest.vue';
import EditaAcount from './components/editaccount.vue';
import work from './components/setting/work.vue';
import proposalsComponent from './components/proposals.vue';
import TimeAgo from './components/TimeAgo.vue';
import message from './components/messages/message.vue';



const app = createApp({});
app.component('time-ago', TimeAgo);
app.mount('#app');

const selestF = createApp({});
selestF.component('selest-component', selestForm);
selestF.mount('#selestF');

const editaccount = createApp({});
editaccount.component('editaccount-component', EditaAcount);
editaccount.mount('#editaccount');

const worksetting = createApp({});
worksetting.component('work-component', work);
worksetting.mount('#work-setting');

//proposals
const app2 = createApp({});
app2.component("proposals-component", proposalsComponent);
app2.mount("#app2");

//messages
const messages = createApp({});
messages.component("messages-component", message);
messages.mount("#message");

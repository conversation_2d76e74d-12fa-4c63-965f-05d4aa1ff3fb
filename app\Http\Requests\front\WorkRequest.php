<?php

namespace App\Http\Requests\front;

use Illuminate\Foundation\Http\FormRequest;

class WorkRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:100',
            'description' => 'required|string|max:600',
            'department_id' => 'required',
            'expiration_date' => 'required',
            'place' => 'required',
            'amount' => 'required|numeric',
        ];
    }

    public function messages()
    {
        return [

            'name.required' => 'الرجاء ادخال اسم المشروع',
            'description.required' => 'الرجاء ادخال  تفاصيل المشروع ',
            'department_id.required' => 'الرجاء ادخال  اسم القسم  ',
            'expiration_date.required' => 'الرجاء ادخال تاريخ الانتهاء  ',
            'place.required' => 'الرجاء تحدبد مكان العمل  ',

      
            'description.max' => 'الحد الاقصي للحروف 600 حرف  في التفاصيل ',
            'name.max' => 'الحد الاقصي للحروف 255 حرف اسم المشروع',
      

        ];
    }
}

<head>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

@extends('front.layout.layout')

@section('body')

    @include('include.alert')


    <main class="container">
        <div class="profile-overview">
            <div class="profile-image-container">
                @if ($user->verified == 1)
                    <div class="profile-activity">
                        <img class="activity" src="{{ asset('assets/imgs/icons/activity-03.png') }}" alt="activity">
                    </div>
                @endif
                <img src="{{ asset($user->avater) }}" alt="الصورة الشخصية" id="profile-image">
            </div>

            <div class="profile-name-container">
                <h2 id="user-name"> {{  $user->name }}</h2>
                <h4 id="job" style="color: #333; "> ابحث عن عمل</h4>
            </div>

            <div class="profile-tabs">
                <button class="tab-button active" data-tab="info">المعلومات الشخصية</button>
                @if ($user->account_type == 1)
                    <button class="tab-button" data-tab="portfolio">معرض الأعمال</button>
                    <button class="tab-button" data-tab="projects">التقيمات</button>
                @endif
            </div>

            <div class="tab-content" id="info-tab">
                <div class="profile-info-section">
                    <h3>معلومات الاتصال</h3>
                    <div class="info-item">
                        <div class="input">
                            <div class="info-label">البريد الإلكتروني</div>
                            <div class="info-value" id="user-email"> {{ $user->email }}</div>
                        </div>
                        <div class="stutes">
                            <span>test</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="input">
                            <div class="info-label">رقم الهاتف</div>
                            <div class="info-value" id="user-phone"> {{  $user->phon }}</div>
                        </div>
                        <div class="stutes">
                            <span>test</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="input">
                            <div class="info-label">الموقع</div>
                            <div class="info-value" id="user-location"> {{  $user->address }}</div>
                        </div>
                        <div class="stutes">
                        </div>
                    </div>
                </div>

                <div class="profile-info-section">
                    <h3>نبذة عن - {{ $user->name }}</h3>
                    <p class="user-bio" id="user-bio">
                        {{  $user->bio }}
                    </p>
                </div>
            </div>

            <div class="tab-content hidden" id="portfolio-tab">
                <div class="portfolio-header">
                    <h3>معرض الأعمال</h3>
                </div>
                @empty($projects)
                    <div class="empty-icon" style="text-align: right">📁</div>
                    <h3>لا توجد مشاريع حالية</h3>
                    <p>لم تقم بإضافة أي مشاريع بعد.</p>
                @endempty

                <div class="portfolio-grid" id="portfolio-items">

                    @foreach ($projects as $project)
                        <div class="portfolio-item">
                            <div class="portfolio-image">
                                <img src="{{ asset($project->image) }}" alt="{{ $project->name }}">
                                <div class="portfolio-overlay">
                                    <div class="portfolio-actions">

                                        <a class="view-portfolio-btn"
                                            href="{{ route('user.profile.show.Project', $project->id) }}">عرض</a>
                                    </div>
                                </div>
                            </div>
                            <div class="portfolio-info">
                                <h4> {{ $project->name }}</h4>
                                <p>{{ $project->description }} </p>
                                <p>{{ $project->job->name }} </p>
                            </div>
                        </div>
                    @endforeach
                </div>

            </div>

            <div class="tab-content hidden" id="projects-tab">

                {{-- @empty() --}}
                <div class="empty-state">
                    <div class="empty-icon">⭐</div>
                    <h3>تقييمات </h3>
                    <p>لم تحصل على أي تقييمات بعد.</p>
                </div>
                {{-- @endempty --}}
                <div>

                </div>
            </div>

            <div class="tab-content hidden" id="reviews-tab">
            </div>
        </div>
    </main>


    @section('scriptFile')
        <script src="{{ asset('front/asset/js/profile.js') }}"></script>
    @endsection
@endsection

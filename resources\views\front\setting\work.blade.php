@extends('front.layout.layout')

@section('body')

 @include('include.alert') 
    <main class="container">
     @vite(['resources/css/app.css', 'resources/js/app.js']) 

        <div class="profile-overview">
            <div class="profile-image-container">

                <img id="imagePreview" class="img"  src="{{asset(Auth::guard()->user()->avater)}}" alt="الصورة الشخصية">
            </div>
            
            <div class="profile-name-container">
                <h4 id="job" style="color: #333; ">  {{Auth::guard()->user()->name}}</h4>
            </div>
            
            <div class="profile-tabs">
                <button class="tab-button active" data-tab="info">تعديل  القسم او الوظيفة</button>
           
            </div>
            
            <div class="tab-content" id="info-tab">
                <div class="profile-info-section">
                    <div id="work-setting">
                    <form action="{{route('user.setting.update')}}" method="POST" >
                        @csrf
                        <h3>معلومات الوظيفة</h3>
                        <work-component></work-component>
                        <button  class="edit-profile-btn">حفظ التعديلات </button>
                    </form>
                    </div>
                </div>
            </div>     
                                     
        </div>
    </main>

    @section('scriptFile')
        <script src="{{asset('front/asset/js/menu-toggle.js')}}"></script>
    @endsection
@endsection
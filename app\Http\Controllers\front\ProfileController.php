<?php

namespace App\Http\Controllers\front;

use App\Http\Controllers\Controller;
use App\Http\Requests\front\freelancer_setupRequest;
use App\Http\Requests\front\porfile\PorfileInfoRequest;
use App\Models\front\Job_title;
use App\Models\front\User;
use App\Models\front\Categorie;
use App\Models\front\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use App\Helpers\uplodeImage;
use App\Http\Requests\front\freelancerSetupRequst;
use App\Models\front\Message;
use App\Models\front\Proposal;
use App\Models\front\Work;
use App\Models\Proposal_decision;
// use DB;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Support\Facades\DB;
use function PHPUnit\Framework\isEmpty;

class ProfileController extends Controller
{

    public function index()
    {

        $projects = Project::with(['category', 'job'])->where('user_id', Auth::user()->id)->get();

        $works = DB::table('works as p')
            ->join('categories as fc', 'p.category_id', '=', 'fc.id')
            ->select(
                'p.*',
                DB::raw("
              (
                CASE
                    WHEN p.created_at >= NOW() - INTERVAL 7 DAY THEN 50
                    WHEN p.created_at >= NOW() - INTERVAL 30 DAY THEN 25
                    ELSE 0
                END
                +
                CASE WHEN fc.id IS NOT NULL THEN 50 ELSE 0 END
            ) AS score
        ")
            )
            ->where('fc.id',  Auth::user()->categorie_id)
            // ->where('p.status', 1)
            ->orderByDesc('score')
            ->orderByDesc('p.created_at')
            ->get();

        //  $works = Work::latest()->get();


        $inboxs = DB::table('messages')
            ->join('users', function ($join) {
                $join->on(DB::raw('CASE
                              WHEN messages.sender_id = ' . Auth::user()->id . '
                              THEN messages.receiver_id
                              ELSE messages.sender_id
                            END'), '=', 'users.id');
            })
            ->select(
                'users.id as user_id',
                'users.name',
                'users.avater',
                DB::raw('COUNT(messages.id) as messages_count'),
                DB::raw('MAX(messages.created_at) as last_time'),
                DB::raw('(SELECT message
                  FROM messages m
                  WHERE ((m.sender_id = ' . Auth::user()->id . ' AND m.receiver_id = users.id)
                         OR (m.receiver_id = ' . Auth::user()->id . ' AND m.sender_id = users.id))
                  ORDER BY m.created_at DESC
                  LIMIT 1) as last_message')
            )
            ->where(function ($query) {
                $query->Where('messages.receiver_id', Auth::user()->id);
            })
            ->groupBy('users.id', 'users.name', 'users.avater')
            ->orderBy('last_time', 'DESC')
            ->get();

        $proposals = Proposal_decision::where('freelance_id', Auth::user()->id)

            ->join('works', 'works.id', 'proposal_decisions.work_id')

            ->select('proposal_decisions.id', 'name', 'proposal_decisions.created_at', 'is_read')

            ->latest('proposal_decisions.created_at')

            ->get();

        return    view('front.profile.index', compact('proposals', 'inboxs', 'projects', 'works',));
    }


    public function userProfile()
    {

        $works =  $work = Work::with(['userWork', 'categoryWork'])

            ->where('user_id', Auth::user()->id)

            ->latest('works.created_at')

            ->get();

        $proposals = Proposal::where('user_id', Auth::user()->id)

            ->join('users', 'users.id', '=', 'Proposals.freelance_id')


            ->select('users.name', 'status', 'proposals.work_id', 'proposals.created_at', 'proposals.message', 'proposals.updated_at')

            ->where('status', '=', 1)

            ->latest()

            ->paginate(10);

        // return $proposals ;

        $inboxs = DB::table('messages')
            ->join('users', function ($join) {
                $join->on(DB::raw('CASE
                        WHEN messages.sender_id = ' . Auth::id() . '
                        THEN messages.receiver_id
                        ELSE messages.sender_id
                    END'), '=', 'users.id');
            })
            ->select(
                'users.id as user_id',
                'users.name',
                'users.avater',
                DB::raw('COUNT(messages.id) as messages_count'),
                DB::raw('MAX(messages.created_at) as last_time'),
                DB::raw('(SELECT message
                  FROM messages m
                  WHERE ((m.sender_id = ' . Auth::id() . ' AND m.receiver_id = users.id)
                         OR (m.receiver_id = ' . Auth::id() . ' AND m.sender_id = users.id))
                  ORDER BY m.created_at DESC
                  LIMIT 1) as last_message'),
                DB::raw('(SELECT COUNT(*)
                  FROM messages m
                  WHERE m.sender_id = users.id
                    AND m.receiver_id = ' . Auth::id() . '
                    AND m.is_read = 0) as unread_count') // عدد الرسائل الغير مقروءة من هذا المستخدم
            )
            ->where(function ($query) {
                $query->where('messages.receiver_id', Auth::id())
                    ->orWhere('messages.sender_id', Auth::id());
            })
            ->groupBy('users.id', 'users.name', 'users.avater')
            ->orderBy('last_time', 'DESC')
            ->get();

        $read = Message::where(function ($q) {
            $q->where('sender_id', Auth::user()->id)
                ->orWhere('receiver_id', Auth::user()->id);
        })
            ->where('is_read', 0)
            ->count();


        return view('front.profile.usrePorfile', compact('read', 'works', 'proposals', 'inboxs'));
    }

    public function userProfileAPI($id)
    {

        //$works =  $work = Work::with(['userWork', 'categoryWork'])->where('user_id' , Auth::user()->id)->get();
        //  $works = Work::where('')->first();

        $proposals = Proposal::where('Proposals.user_id', $id)

            ->join('users', 'users.id', '=', 'Proposals.freelance_id')


            ->join('works', 'works.id', '=', 'Proposals.work_id')

            ->select(
                'proposals.id',
                'users.name',
                'proposals.work_id',
                'proposals.created_at',
                'works.name as workTitel',

                'proposals.message',
                'proposals.freelance_id',
                'proposals.updated_at'
            )

            ->where('proposals.status', '=', 1)


            ->latest()

            ->get();

        return   json_decode($proposals);
    }

    public function update_photo(Request $request)
    {

        // validate
        $pass = $request->validate(['img' => 'required|image']);

        if (!$pass) {

            return Redirect()->back()->with('error', 'something worg try agn');
        }

        $user = User::where('id', Auth::user()->id)->first();

        if (!empty($request->img)) {

            $imge = uplodeImage::uplode($request->img, 'assets/imgs/Profile/');

            $user->update(['avater' =>  $imge]);
        }

        return Redirect()->route('user.account-type');
    }


    public  function account_type()
    {

        return view('front.profile.account-type');
    }

    public function account_type_update(Request $request)
    {

        $user = User::where('id', Auth::user()->id)->first();

        if ($user) {

            $user->update(['account_type' =>  $request->account]);

            if ($request->account == 1) {

                return Redirect()->route('user.freelancer_profile_setup');
            }
            return Redirect()->route('user.employer_profile_setup');
        }

        return Redirect()->route('user.profile.index');

    }

    public function freelancer_profile_setup()
    {

        $jobs = Job_title::select()->get();

        $departments = Categorie::all();
        // return  $jobs ;

        return view('front.profile.freelancer-setup-profile', compact('jobs', 'departments'));
    }

    public function freelancer_save_setup(freelancerSetupRequst $request)
    {

        $user = User::where('id', Auth::user()->id)->first();


        $user->update([
            'experince' => $request->experince,
            'address' => $request->address,
            'bio' => $request->bio,
            'phon' => $request->phone,
            'job_id' => $request->job_id,
            'categorie_id' => $request->department_id,
            'updated_at' => NOW()

        ]);

        if ($user) {

            return view('front.profile.identity-verification');
        }

        return Redirect()->back()->with('error', 'email or password was rong try agn');
    }

    public function employer_setup()
    {

        return view('front.profile.employer-profile-setup');
    }

    public function employer_save_setup(Request $request)
    {

        $pass = $request->validate([
            'address' => 'required|string|max:255',
            'phone' => 'required|string|max:255',
            'bio' => 'required|string|max:600'
        ]);

        if (!$pass) {
            return Redirect()->back()->with('error', 'البيانات');
        }

        $user = User::where('id', Auth::user()->id)->first();

        $user->update([
            'address' =>  $request->address,
            'phon' =>  $request->phone,
            'bio' =>  $request->bio

        ]);

        if ($user) {

            return view('front.profile.identity-verification');
        }
    }

    public function identity()
    {

        return view('front.profile.identity-verification');
    }

    public function identity_verification(Request $request)
    {


        $user = User::where('id', Auth::user()->id)->first();

        if (!empty($request->identity)) {

            $imge = uplodeImage::uplode($request->identity, 'assets/imgs/identity/');

            $user->update(['identity' =>  $imge]);
        }

        return redirect()->route('user.registration.complete');
    }

    public function complete()
    {
        return view('front.profile.registration-complete');
    }


    public function edit()
    {
        return   view('front.profile.mange.profile-edit');
    }

    public function update(PorfileInfoRequest $request)
    {

        $user = User::where('id', Auth::user()->id)->first();

        if (!empty($request->img)) {

            $imge = uplodeImage::uplode($request->img, 'assets/imgs/Profile/', $user->avater);

            $user->update(['avater' =>  $imge]);
        }

        $user->update([
            'name' =>  $request->name,
            'address' =>  $request->address,
            'phon' =>  $request->phon,
            'experince' =>  $request->experince,
            'bio' =>  $request->bio

        ]);

        return  Redirect()->back()->with('success', 'تم التحديث بنجاح');
    }

    public function edit_accont()
    {

        return  view('front.profile.mange.edit-accont');
    }

    public function show_Project($id)
    {

        $project = Project::with(['category', 'job'])->where('id', $id)->first();

        $check = false;

        if (Auth::user()->id !==  $project->user_id) {

            $check = true;
        }

        return view('front.show', compact('project', 'check'));
    }

    public function Visit_user($id)
    {

        $user = User::where('id', $id)->first();

        $works =  $work = Work::with(['userWork', 'categoryWork'])

            ->where('user_id', $id)

            ->latest('works.created_at')

            ->get();

        if ($user->account_type == 0) {

            return view('front.profile.visit.user', compact('user', 'works'));
        }

        $projects = Project::with(['category', 'job'])->where('user_id', $id)->get();

        return  view('front.profile.visit.freelance', compact('user', 'projects'));
    }
}

<?php

use App\Http\Middleware\back\AdminAuthenticate;
use App\Http\Middleware\back\RedirectIfLogin;
use App\Http\Middleware\front\profile;
use App\Http\Middleware\front\UserProfile;
use App\Http\Middleware\front\verification;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
        )
        ->withMiddleware(function (Middleware $middleware) {

            $middleware->alias([
             'RedirectIfLogin' =>RedirectIfLogin::class
            ]);
            $middleware->append(RedirectIfLogin::class);
               
    })
        ->withMiddleware(function (Middleware $middleware) {
               
            $middleware->alias([
             'verification' =>verification::class
            ]);
            $middleware->append(verification::class);
               
    })
        ->withMiddleware(function (Middleware $middleware) {
               
            $middleware->alias([
             'profile' =>profile::class
            ]);
            $middleware->append(profile::class);
               
    })
        ->withMiddleware(function (Middleware $middleware) {
               
            $middleware->alias([
             'UserProfile' =>UserProfile::class
            ]);
            $middleware->append(UserProfile::class);
               
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

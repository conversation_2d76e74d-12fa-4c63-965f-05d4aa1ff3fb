<?php

namespace App\Http\Controllers\front;

use App\Http\Controllers\Controller;
use App\Models\front\Indox;
use App\Models\front\Proposal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IndoxController extends Controller
{
    public function index(){

        $proposals = Proposal::where('Proposals.user_id', Auth::user()->id)

      ->join('inboxs' ,'inboxs.proposal_id' ,'=','Proposals.id')

      ->select('inboxs.freelance_id')

      ->get();

        // $inbox = Proposal::select('proposal')->where();

        // $inbox = Indox::select()->where();

        return $proposals;
    }
}

@extends('front.layout.layout')

@section('body')

 @include('include.alert') 
 
    <main class="container">
        <div class="account-type-container">
            <div class="account-type-header">
                  <div class="step-indicator">
                    <div class="step completed">1</div>
                    <div class="step-line"></div>
                    <div class="step completed">2</div>
                    <div class="step-line"></div>
                    <div class="step active">3</div>
                    <div class="step-line"></div>
                    <div class="step">4</div>
                    <div class="step-line"></div>
                    <div class="step">5</div>
                </div>
                <h2>اختر نوع حسابك</h2>
                <p class="step-description">يرجى تحديد نوع الحساب الذي ترغب في إنشائه</p>
            </div>
            
            <div class="account-types">
                <div class="account-type-card" id="employer-card">
                    <div class="account-type-icon">👔</div>
                    <h3>أنا صاحب عمل</h3>
                    <p>أريد توظيف مستقلين لإنجاز مشاريعي</p>
                    <ul class="account-benefits">
                        <li>نشر مشاريع واستقبال عروض من المستقلين</li>
                        <li>التواصل المباشر مع المستقلين المؤهلين</li>
                        <li>إدارة المشاريع ومتابعة سير العمل</li>
                        <li>الدفع الآمن عبر نظام الضمان المالي</li>
                    </ul>
                    <form action="{{route('user.account-type.update')}}" method="POST">
                        @csrf
                        <input type="number" name="account" hidden value="0"> 
                        <button class="account-type-select" data-type="employer">اختيار</button>
                    </form>
                </div>
                
                <div class="account-type-card" id="freelancer-card">
                    <div class="account-type-icon">💼</div>
                    <h3>أنا مستقل</h3>
                    <p>أبحث عن فرص عمل ومشاريع</p>
                    <ul class="account-benefits">
                        <li>البحث عن مشاريع تناسب مهاراتك</li>
                        <li>تقديم عروض للمشاريع المتاحة</li>
                        <li>بناء سمعة مهنية وتقييمات إيجابية</li>
                        <li>استلام المدفوعات بطريقة آمنة وسهلة</li>
                    </ul>
                     <form action="{{route('user.account-type.update')}}" method="POST">
                        @csrf
                        <input type="number" name="account" hidden value="1"> 
                        <button class="account-type-select" data-type="employer">اختيار</button>
                    </form>
                </div>
            </div>
            
            <div class="account-type-footer">
                <p class="account-type-note">يمكنك تغيير نوع حسابك لاحقًا من إعدادات الحساب</p>
            </div>
        </div>
    </main>


    @section('scriptFile')
        <script src="{{asset('front/asset/js/account-type.js')}}"></script>
    @endsection
@endsection
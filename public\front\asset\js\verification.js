const inputs = document.querySelectorAll('.code-input');
const form = document.getElementById('verification-form');
const submitButton = form.querySelector('button[type="submit"]');
const hiddenInput = document.getElementById('full-code');

inputs.forEach((input, index) => {
    input.setAttribute('inputmode', 'numeric');
    input.setAttribute('pattern', '[0-9]*');

    // عند الإدخال
    input.addEventListener('input', () => {
        input.value = input.value.replace(/[^0-9]/g, '');

        if (input.value.length === 1) {
            if (index < inputs.length - 1) {
                inputs[index + 1].focus();
            } else {
                submitButton.click(); // إرسال تلقائي عند اكتمال الرقم الأخير
            }
        }
    });

    // التنقل للخلف عند الحذف
    input.addEventListener('keydown', (e) => {
        if (e.key === 'Backspace' && input.value === '' && index > 0) {
            inputs[index - 1].focus();
        }
    });

    // لصق الكود كاملًا
    if (index === 0) {
        input.addEventListener('paste', (e) => {
            const paste = e.clipboardData.getData('text').replace(/[^0-9]/g, '').slice(0, inputs.length);
            paste.split('').forEach((char, i) => {
                if (inputs[i]) inputs[i].value = char;
            });
            if (paste.length === inputs.length) {
                submitButton.click();
            }
            e.preventDefault();
        });
    }
});

// قبل الإرسال → جمع القيم في الحقل المخفي
form.addEventListener('submit', (e) => {
    let code = '';
    inputs.forEach(input => code += input.value);
    hiddenInput.value = code;
});

@extends('front.layout.layout')

@section('body')

 @include('include.alert')

     <main class="container">
        <div class="profile-overview">

            <div class="profile-name-container">
                <h4 id="job" style="color: #333; ">  المشاريع</h4>
            </div>

            <div class="profile-tabs">
                <button class="tab-button active" data-tab="info">اضافة مشروع جدبد  </button>

            </div>

            <div class="tab-content" id="info-tab">
                <div class="profile-info-section">
                    <form action="{{route('user.work.save')}}" method="POST" enctype="multipart/form-data">
                        @csrf
                    <h3>معلومات المشروع</h3>
                    <div class="info-item-edit">
                         <div class="info-label">  العنوان  </div>
                        <div class="info-value" id="user-email"><input placeholder="مثلا عامل تبريد و تكيف" type="text" name="name" value=""></div>
                    </div>
                    <div class="info-item-edit">
                         <div class="info-label">وصف  المشروع  </div>
                        <div class="info-value" id="user-email"><textarea type="text" name="description" style="height: 300px"></textarea></div>
                    </div>
                    <div class="info-item-edit">
                         <div class="info-label">  المكان  </div>
                        <div class="info-value" id="user-email"><input placeholder=" موقع العمل" type="text" name="place" value=""></div>
                    </div>
            <!-- اختيار القسم -->
                <div class="info-item-edit">
                     <label for="department">اختر القسم</label>
                     <select id="department" name="department_id" required>
                          <option value="">اختر القسم</option>
                          @foreach($departments as $department)
                          <option value="{{ $department->id }}">{{ $department->name }}</option>
                    @endforeach
                    </select>
                </div>
                <div class="info-item-edit">
                    <div class="info-label">   الميزانية بالجنية السوداني </div>
                        <div class="info-value" id="user-email"><input placeholder=" مثلا 10000ج" type="number" name="amount" value=""></div>
                    </div>
                <div class="info-item-edit">
                    <div class="info-label"> عدد ايام التسليم </div>
                        <div class="info-value" id="user-email"><input placeholder="ايام التسليم" type="text" name="expiration_date" value=""></div>
                    </div>
                <div class="profile-actions">
                        <button  class="edit-profile-btn">حفظ  </button>
                    </div>
                    </form>

               </div>
            </div>

        </div>
    </main>




    @section('scriptFile')
        <script src="{{asset('front/asset/js/menu-toggle.js')}}"></script>
    @endsection
@endsection

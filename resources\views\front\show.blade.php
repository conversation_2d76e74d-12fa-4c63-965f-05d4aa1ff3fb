@extends('front.layout.layout')

@section('body')

    @include('include.alert')




    <div class="container">
        <div class="card project-card">
            <img src="{{ asset($project->image) }}" alt="صورة المشروع" class="img-fluid project-image">
            <div class="card-body">
                <h3 class="card-title"> {{ $project->name }}</h3>
                <p class="card-text"> {{ $project->description }} </p>
                <ul class="list-group list-group-flush mb-4">
                    <li class="list-group-item"><strong>التصنيف:</strong> {{ $project->category->name }}</li>
                    <li class="list-group-item"><strong>تاريخ الإضافة:</strong> {{ $project->created_at->format('Y-m-d') }}
                    </li>
                    {{-- <li class="list-group-item"><strong>المدة:</strong> ٦ أسابيع</li> --}}
                </ul>
                @if (!$check)
                    <div class="d-flex justify-content-between flex-wrap action-buttons">
                        <a href="{{ route('user.projects.edit', $project->id) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>تعديل المشروع
                        </a>
                        <form action="{{ route('user.projects.delete', $project->id) }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash-alt me-2"></i>حذف المشروع
                            </button>
                        </form>
                        <a href="{{ route('user.projects.redirect') }}" class="btn btn">
                            <i class="fas fa-arrow-right me-2"></i>رجوع للمعرض
                        </a>
                    </div>
                @endif

            </div>
        </div>
    </div>


@section('scriptFile')
    <script src="{{ asset('front/asset/js/menu-toggle.js') }}"></script>
@endsection
@endsection

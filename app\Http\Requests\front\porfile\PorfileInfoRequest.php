<?php

namespace App\Http\Requests\front\porfile;

use Illuminate\Foundation\Http\FormRequest;

class PorfileInfoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:20',
            // 'email' => 'required|email',
            'phon' => 'required|max:255',
            'address' => 'required|string|max:255',
            'bio' => 'string|max:600',
            'experince' => 'required',
        ];
    }

    public function messages()
    {
        return [

            'name.required' => 'الرجاء ادخال اسم ',
            'phon.required' => 'الرجاء ادخال  رقم الهاتف ',
            'address.required' => 'الرجاء ادخال  العنوان   ',

        ];
    }
}

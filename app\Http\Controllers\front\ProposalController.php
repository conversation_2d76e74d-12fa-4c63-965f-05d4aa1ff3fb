<?php

namespace App\Http\Controllers\front;

use App\Http\Controllers\Controller;
use App\Models\front\Proposal;
use App\Models\front\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProposalController extends Controller
{
    public function index($id) {

        $Proposal = Proposal::where('proposals.id',$id)

         ->join('users' ,'users.id' ,'=','proposals.freelance_id')

        ->join('categories','categories.id' ,'=','proposals.categorie_id')

        ->join('works','works.id' ,'=','proposals.work_id')

         ->select('proposals.id as proposalsID','work_id','works.status as st','proposals.amount','proposals.DeliveryDate','proposals.message','proposals.created_at'

         ,'users.name as user_name','users.id as userID' ,'users.avater','users.identity_verification',

         'users.address','users.created_at as user_created_at','users.project_count',

         'categories.name as cat_name')

        ->first();

        $arrOfId = [
            'uID'=> $Proposal->userID,
            'WID'=> $Proposal->work_id
        ];
        return   view("front.Proposal.index" ,compact("Proposal",'arrOfId'));
    }

    public function reject($id) {

        $Proposal = Proposal::where('proposals.id',$id)->where('user_id',Auth::user()->id)->first();

        $Proposal->update(['status' =>  0]);

        return redirect()->route('user.profile.user.index')->with('success','تم رفض العرض');

    }
}










<?php

namespace App\Http\Requests\front;

use Illuminate\Foundation\Http\FormRequest;

class RegisterReques extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules()
    {
        return [
            'email' => 'required|max:255|email|unique:users',
            'name' => 'required|max:255|string',
            'password' => 'required|max:255|string|min:8|confirmed',
        ];
    }

    public function messages()
    {
        return [

            'email.required' => 'الرجاء ادخال اسم المستخدم',
            'email.max' => 'الحد الاقصي للحروف 100 حرف ',
            'email.email' => 'استخدم الاحرف الانجلزية ABC وصيغة الاميل ***@gmail.com',

            'password.required' => 'الرجاء ادخال  كلمة المرور',
            'password.max' => 'الحد الاقصي للحروف 255 حرف '

        ];
    }
}

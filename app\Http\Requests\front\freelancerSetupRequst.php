<?php

namespace App\Http\Requests\front;

use Illuminate\Foundation\Http\FormRequest;

class freelancerSetupRequst extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'department_id' =>'required',
            'job_id'=>'required',
            'experince'=>'required',
            'bio'=>'max:600',
            'phone' =>'required',
            'address'=>'required'
        ];
    }


        public function messages()
    {
        return [

            'phone.required' => 'الرجاء ادخال رقم الهاتف ',
            'address.required' => 'الرجاء ادخال العنوان ',
            'bio.max' => 'الحد الاقصي للحروف 600 حرف ',

        ];
    }
}

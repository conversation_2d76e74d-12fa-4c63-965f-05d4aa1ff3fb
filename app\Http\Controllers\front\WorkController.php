<?php

namespace App\Http\Controllers\front;

use App\Http\Controllers\Controller;
use App\Http\Requests\front\WorkRequest;
use App\Models\front\Categorie;
use App\Models\front\Proposal;
use App\Models\front\User;
use App\Models\front\Work;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;

class WorkController extends Controller
{
    public function index() {

      $work = Work::with(['userWork', 'categoryWork'])->get();

      return $work;

    }

    public function create () {

      $departments = Categorie::select('id','name')->get();

      return view('front.work.create',compact('departments'));
    }

    public function save(WorkRequest $request) {



        $user = User::select()->where('id',Auth::user()->id)->first();

        if($user->project_count >= 10 ) {

         return Redirect()->back()->with('error',  'الحد الاعلي للمشاريع 10 فقد ');

        }

        $user->update(['project_count'=>$user->project_count+1 ]);

       $work = work::create([
            'name' => $request->name,
            'description' =>$request->description,
            'amount' =>$request->amount,
            'place' =>$request->place,
            'image' =>'',
            'user_id' =>Auth::user()->id,
            'category_id' =>$request->department_id,
            'expiration_date' =>$request->expiration_date,
            'updated_at'=>NOW(),
            'created_at'=>NOW()
        ]);

        if($work) {
          return  Redirect()->back()->with('success','تم الاضافة بنجاح');
        }

        return Redirect()->back()->with('error','حدث خطاء ما الرجاء المحاولة مرة اخره');


    }

    public function edit ($id) {


      $departments = Categorie::select('id','name')->get();

      $work = Work::with(['userWork', 'categoryWork'])->where('id' ,$id)->first();

      if( Auth::user()->id !==  $work->user_id) {
        return   Redirect()->back()->with('error','العنصر غير متاح ');
       }


      return view('front.work.edit',compact('work','departments'));
    }

    public function update(WorkRequest $request ,$id) {

             $work = Work::where('id' ,$id)->first();


     if( Auth::user()->id !==  $work->user_id) {
        return   Redirect()->back()->with('error','العنصر غير متاح ');
       }

             $work->update([
            'name' => $request->name,
            'description' =>$request->description,
            'amount' =>$request->amount,
            'place' =>$request->place,
            'image' =>'',
            'user_id' =>Auth::user()->id,
            'category_id' =>$request->department_id,
            'status' =>$request->status,
            'expiration_date' =>$request->expiration_date,
            'updated_at'=>NOW(),
        ]);

        if($work) {
          return  Redirect()->back()->with('success','تم التحديث بنجاح');
        }

        return Redirect()->back()->with('error','حدث خطاء ما الرجاء المحاولة مرة اخره');
    }

    public function display($id) {

      $work = Work::with(['userWork', 'categoryWork'])->where('id' ,$id)->first();

      $ProposalCount = Proposal::where('work_id' ,$id)->count();

      $check = false;

      if( Auth::user()->id !==  $work->user_id) {

        $check = true;
      }


      return  view('front.work.display',compact('check','work','ProposalCount',));

    }

   public function delete($id) {


       $user = User::select()->where('id',Auth::user()->id)->first();



       if($user->project_count == 0 ) {

         return Redirect()->back()->with('error','حدث خطاء ما الرجاء المحاولة مرة اخره');

        }

        $project = Work::where('id', $id) ->first();

         if( Auth::user()->id !==  $project->user_id) {
        return   Redirect()->back()->with('error','العنصر غير متاح ');
       }

        $user->update(['project_count'=>$user->project_count-1 ]);
        if($project) {

             if (!empty($project->image)){

                 File::delete(public_path($project->image));
              }

             $project->delete();

             return  Redirect()->route('user.profile.user.index')->with('success','تم الحذف بنجاح');
        }

    }

    public function back () {

        return redirect()->route('user.profile.user.index');
    }
}

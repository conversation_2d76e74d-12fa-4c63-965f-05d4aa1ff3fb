<?php

namespace App\Http\Middleware\front;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class verification
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
      if (Auth::guard('web')->check() && Auth::guard('web')->user()->verification_email == 0) {
            
        return Redirect()->route('user.verification');
      }
        return $next($request);
    }
}

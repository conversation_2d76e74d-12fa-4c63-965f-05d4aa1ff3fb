// Skills input handling with max 5 skills
const skillsInput = document.getElementById('skills-input');
const skillsContainer = document.getElementById('skills-container');
const skillsHidden = document.getElementById('skills');
let skillsArray = [];
const maxSkills = 5;

// Character counter for bio
const bioTextarea = document.getElementById('bio');
const bioCount = document.getElementById('bio-count');

bioTextarea.addEventListener('input', function () {
    const count = this.value.length;
    bioCount.textContent = count;

    if (count > 500) {
        bioCount.style.color = 'red';
    } else {
        bioCount.style.color = '';
    }
});


skillsInput.addEventListener('keydown', function (e) {
    if (e.key === 'Enter' || e.key === ',') {
        e.preventDefault();

        const skill = this.value.trim();

        if (skillsArray.length >= maxSkills) {
            alert('لا يمكنك إضافة أكثر من 5 مهارات.');
            this.value = '';
            return;
        }

        if (skill && !skillsArray.includes(skill)) {
            addSkill(skill);
            this.value = '';
        }
    }
});

function addSkill(skill) {
    skillsArray.push(skill);

    const skillTag = document.createElement('div');
    skillTag.className = 'skill-tag';
    skillTag.innerHTML = `${skill} <span class="remove-skill" data-skill="${skill}">×</span>`;
    skillsContainer.appendChild(skillTag);

    skillsHidden.value = skillsArray.join(',');

    skillTag.querySelector('.remove-skill').addEventListener('click', function () {
        const skillToRemove = this.getAttribute('data-skill');
        removeSkill(skillToRemove, skillTag);
    });
}

function removeSkill(skill, element) {
    skillsArray = skillsArray.filter(s => s !== skill);
    element.remove();
    skillsHidden.value = skillsArray.join(',');
}

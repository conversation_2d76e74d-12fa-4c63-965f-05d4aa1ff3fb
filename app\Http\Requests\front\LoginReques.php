<?php

namespace App\Http\Requests\front;

use Illuminate\Foundation\Http\FormRequest;

class LoginReques extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
   public function rules()
    {
        return [
            'email' => 'required|email',
            'password' => 'required|string',
        ];
    }

    public function messages()
    {
        return [

            'email.required' => 'الرجاء ادخال اسم المستخدم',
            'email.email' => 'استخدم الاحرف الانجلزية ABC وصيغة الاميل ***@gmail.com',

            'password.max' => 'الحد الاقصي للحروف 255 حرف '

        ];
    }
}

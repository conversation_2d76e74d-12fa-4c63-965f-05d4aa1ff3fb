    .job-acceptance-container {
      max-width: 900px;
      margin: 2rem auto;
      padding: 2.5rem;
      background: #fff;
      border-radius: 20px;
      box-shadow: 0 15px 40px rgba(0,0,0,0.08);
      animation: fadeIn 0.6s ease-out;
      position: relative;
      overflow: hidden;
    }

    .job-acceptance-container::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      height: 5px;
      background: linear-gradient(to left, var(--primary), var(--success));
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(30px); }
      to { opacity: 1; transform: translateY(0); }
    }

    h2, h3 {
      margin-top: 0;
      color: var(--gray-900);
    }

    .acceptance-header {
      text-align: center;
      margin-bottom: 2.5rem;
      padding: 2rem;
      background: linear-gradient(135deg, #dbeafe 0%, #eff6ff 100%);
      border-radius: 16px;
      position: relative;
      overflow: hidden;
    }

    .acceptance-header::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 100px;
      height: 100px;
      background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6' opacity='0.1'%3E%3Cpath d='M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z'/%3E%3C/svg%3E") no-repeat center center;
      background-size: contain;
    }

    .success-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      color: var(--success);
      animation: pulse 1.5s infinite;
      display: inline-block;
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.08); }
      100% { transform: scale(1); }
    }

    .acceptance-message {
      font-size: 1.1rem;
      color: var(--gray-700);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.8;
    }

    .card {
      margin-bottom: 2rem;
      padding: 1.8rem;
      border: 1px solid var(--gray-300);
      border-radius: 16px;
      background: var(--gray-100);
      transition: transform 0.3s, box-shadow 0.3s;
      position: relative;
      overflow: hidden;
    }

    .card:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 30px rgba(0,0,0,0.09);
    }

    .card::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 5px;
      height: 100%;
      background: linear-gradient(to bottom, var(--primary), var(--accent));
      transition: width 0.3s;
    }

    .card:hover::after {
      width: 7px;
    }

    .info-item {
      margin-bottom: 0.8rem;
      display: flex;
      align-items: center;
      padding: 0.5rem 0;
    }

    .info-label {
      font-weight: 700;
      margin-left: 0.5rem;
      color: var(--gray-900);
      min-width: 130px;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .info-label i {
      color: var(--primary);
      font-size: 1.1rem;
    }

    .status-badge {
      padding: 0.4rem 1rem;
      border-radius: 20px;
      font-size: 0.85rem;
      font-weight: 600;
      display: inline-flex;
      align-items: center;
      gap: 0.4rem;
    }

    .in-progress {
      background: #fffbeb;
      color: #d97706;
      border: 1px solid #fcd34d;
    }

    .completed {
      background: #ecfdf5;
      color: #065f46;
      border: 1px solid #34d399;
    }

    .worker-profile {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }

    .worker-avatar {
      position: relative;
    }

    .worker-avatar img {
      width: 90px;
      height: 90px;
      border-radius: 50%;
      border: 3px solid var(--gray-300);
      transition: border-color 0.3s;
      object-fit: cover;
    }

    .worker-profile:hover .worker-avatar img {
      border-color: var(--primary);
    }

    .worker-avatar::after {
      content: '';
      position: absolute;
      bottom: 5px;
      right: 5px;
      width: 17px;
      height: 17px;
      background-color: var(--success);
      border-radius: 50%;
      border: 2px solid white;
      z-index: 2;
    }

    .worker-info h4 {
      margin-bottom: 0.3rem;
      color: var(--gray-900);
      font-size: 1.3rem;
    }

    .worker-title {
      color: var(--gray-700);
      margin-bottom: 0.5rem;
      font-size: 1rem;
    }

    .worker-rating {
      display: flex;
      align-items: center;
      gap: 0.3rem;
      color: var(--warning);
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .worker-contact {
      color: var(--gray-700);
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .management-actions {
      display: flex;
      gap: 1rem;
      margin-bottom: 1.5rem;
      flex-wrap: wrap;
    }


    .success-button, .secondary-button, .outline-button, .primary-button {
      padding: 0.9rem 1.7rem;
      border-radius: 12px;
      font-weight: 600;
      font-size: 1rem;
    }

    .success-button {
      background: var(--success);
      color: #fff;
    }

    .success-button:hover {
      background: var(--success-dark);
      transform: translateY(-3px);
      box-shadow: 0 6px 15px rgba(16, 185, 129, 0.35);
    }

    .secondary-button {
      background: var(--primary);
      color: #fff;
    }

    .secondary-button:hover {
      background: var(--primary-dark);
      transform: translateY(-3px);
      box-shadow: 0 6px 15px rgba(59, 130, 246, 0.35);
    }

    .outline-button {
      background: transparent;
      border: 1px solid var(--gray-400);
      color: var(--gray-700);
    }

    .outline-button:hover {
      background: var(--gray-200);
      transform: translateY(-3px);
      box-shadow: 0 6px 15px rgba(0,0,0,0.1);
    }

    .primary-button {
      background: var(--primary);
      color: #fff;
    }

    .primary-button:hover {
      background: var(--primary-dark);
      transform: translateY(-3px);
      box-shadow: 0 6px 15px rgba(59, 130, 246, 0.35);
    }

    .progress-tracker {
      display: flex;
      justify-content: space-between;
      margin-top: 1.5rem;
      position: relative;
    }

    .progress-tracker::before {
      content: '';
      position: absolute;
      top: 20px;
      right: 0;
      left: 0;
      height: 4px;
      background: var(--gray-300);
      z-index: 1;
      border-radius: 2px;
    }

    .progress-step {
      text-align: center;
      flex: 1;
      position: relative;
      z-index: 2;
    }

    .step-circle {
      width: 40px;
      height: 40px;
      line-height: 40px;
      border-radius: 50%;
      margin: 0 auto 0.7rem;
      background: var(--gray-300);
      color: var(--gray-700);
      font-weight: 700;
      position: relative;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .progress-step.completed .step-circle {
      background: var(--primary);
      color: #fff;
      box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.25);
    }

    .progress-step.active .step-circle {
      background: #fff;
      color: var(--primary);
      border: 2px solid var(--primary);
      box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.2);
    }

    .progress-step span {
      font-size: 0.95rem;
      font-weight: 600;
      color: var(--gray-700);
    }

    .progress-step.completed span {
      color: var(--primary);
    }

    .progress-step.active span {
      color: var(--gray-900);
      font-weight: 700;
    }

    .star-rating {
      display: flex;
      gap: 0.3rem;
      margin: 1.2rem 0;
    }

    .star {
      font-size: 2.2rem;
      cursor: pointer;
      color: var(--gray-300);
      transition: color 0.2s, transform 0.2s;
    }

    .star:hover {
      transform: scale(1.2);
    }

    .star.active {
      color: var(--warning);
    }

    .rating-text {
      margin-top: 0.5rem;
      font-weight: 500;
      color: var(--gray-700);
      font-size: 1.1rem;
    }

    .comment-section {
      margin: 1.8rem 0;
    }

    .comment-section label {
      display: block;
      margin-bottom: 0.7rem;
      font-weight: 600;
      color: var(--gray-900);
      font-size: 1.1rem;
    }

    .comment-section textarea {
      width: 100%;
      padding: 1.2rem;
      border: 1px solid var(--gray-300);
      border-radius: 12px;
      font-family: inherit;
      resize: vertical;
      transition: border-color 0.3s, box-shadow 0.3s;
      font-size: 1rem;
    }

    .comment-section textarea:focus {
      outline: none;
      border-color: var(--primary);
      box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15);
    }

    .rating-actions {
      display: flex;
      gap: 1rem;
    }

    .additional-actions {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
      margin-top: 2rem;
    }

    .additional-actions a {
      text-decoration: none;
    }

    /* تأثيرات خاصة */
    .confetti {
      position: absolute;
      width: 10px;
      height: 10px;
      background-color: var(--primary);
      opacity: 0.7;
      border-radius: 50%;
      animation: confetti-fall 5s linear infinite;
    }

    @keyframes confetti-fall {
      0% {
        transform: translateY(-100px) rotate(0deg);
        opacity: 1;
      }
      100% {
        transform: translateY(100vh) rotate(360deg);
        opacity: 0;
      }
    }

    /* Responsive design */
    @media (max-width: 768px) {

      .worker-profile {
        flex-direction: column;
        text-align: center;
      }

      .management-actions, .rating-actions, .additional-actions {
        flex-direction: column;
      }

      .progress-tracker {
        flex-direction: column;
        gap: 2rem;
      }

      .progress-tracker::before {
        display: none;
      }

      .progress-step {
        display: flex;
        align-items: center;
        text-align: right;
      }

      .step-circle {
        margin: 0 1rem 0 0;
      }

      .job-acceptance-container {
        padding: 1.5rem;
        margin: 1rem auto;
      }

      .acceptance-header {
        padding: 1.5rem;
      }
    }

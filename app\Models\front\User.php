<?php

namespace App\Models\front;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;


class User extends Authenticatable
{
    protected $fillable = [
       'name', 'email', 'password', 'avater', 'verification_email', 'phon',
       'code','account_type','created_at','updated_at','experince','job_id',
       'categorie_id','project_count','verified','tamp_email',
       'address','bio','identity' ,'identity_verification'
    ];

    public function WorkUser()
    {
        return $this->hasMany(Work::class, 'user_id');
    }
    public function ProposalUser()
    {
        return $this->hasMany(Proposal::class, 'user_id');
    }

   // public $timestamps = false;
}

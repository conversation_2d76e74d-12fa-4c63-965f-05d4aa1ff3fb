<head>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

@extends('front.layout.layout')

@section('body')

    @include('include.alert')



    <main class="container">
        <div class="profile-overview">
            <div class="profile-image-container">
                @if (Auth::guard()->user()->verified == 1)
                    <div class="profile-activity">
                        <img class="activity" src="{{ asset('assets/imgs/icons/activity-03.png') }}" alt="activity">
                    </div>
                @endif
                <img src="{{ asset(Auth::guard()->user()->avater) }}" alt="الصورة الشخصية" id="profile-image">
            </div>

            <div class="profile-name-container">
                <h2 id="user-name"> {{ Auth::guard()->user()->name }}</h2>
                <h4 id="job" style="color: #333; "> ابحث عن عمل</h4>
            </div>

            <div class="profile-tabs">
                <button class="tab-button active" data-tab="info">الرئيسية</button>
                @if (Auth::guard()->user()->account_type == 1)
                    <button class="tab-button" data-tab="portfolio">معرض الأعمال</button>
                    <button class="tab-button" data-tab="projects">المشاريع</button>
                    <button class="tab-button" data-tab="reviews">صندوق الوارد</button>
                @endif
            </div>

            <div class="tab-content" id="info-tab">

                <div class="messages-section">
                    <div class="section-header-messages">
                        <h2>الرسائل والإشعارات</h2>
                        <a href="{{route('user.freelanceDashboard')}}" class="view-all-link">عرض الكل</a>
                    </div>
                    <div class="messages-list" id="messages-list">
                        <div class="messages-list" id="app">

                            @foreach ($proposals as $proposal)
                                <a  href="{{route('user.Proposal_decision.freelance', $proposal->id)}}">
                                    <div class="message-item @if ($proposal->is_read == 0) unread @endif">
                                        <div class="message-header">
                                            <span class="message-sender">{{ $proposal->name }}</span>
                                            <span class="message-time">

                                                <time-ago
                                                    time="{{ \Carbon\Carbon::parse($proposal->created_at)->toIso8601String() }}"></time-ago>

                                            </span>
                                        </div>
                                        <p class="message-content">تم قبول طلب اضغط من اجل التفاصيل</p>
                                    </div>
                                </a>
                            @endforeach


                        </div>
                    </div>
                </div>



                <div class="profile-info-section">
                    <h3>معلومات الاتصال</h3>
                    <div class="info-item">
                        <div class="input">
                            <div class="info-label">البريد الإلكتروني</div>
                            <div class="info-value" id="user-email"> {{ Auth::guard()->user()->email }}</div>
                        </div>
                        <div class="stutes">
                            <span>test</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="input">
                            <div class="info-label">رقم الهاتف</div>
                            <div class="info-value" id="user-phone"> {{ Auth::guard()->user()->phon }}</div>
                        </div>
                        <div class="stutes">
                            <span>test</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="input">
                            <div class="info-label">الموقع</div>
                            <div class="info-value" id="user-location"> {{ Auth::guard()->user()->address }}</div>
                        </div>
                        <div class="stutes">
                            <span class="complet">test</span>
                        </div>
                    </div>
                </div>

                <div class="profile-info-section">
                    <h3>نبذة عني</h3>
                    <p class="user-bio" id="user-bio">
                        {{ Auth::guard()->user()->bio }}
                    </p>
                </div>
            </div>

            <div class="tab-content hidden" id="portfolio-tab">
                <div class="portfolio-header">
                    <h3>معرض الأعمال</h3>
                    <a id="" href="{{ route('user.projects.create') }}" class="add-portfolio-btn btn">إضافة عمل
                        جديد</a>
                </div>

                <div class="portfolio-grid" id="portfolio-items">

                    @foreach ($projects as $project)
                        <div class="portfolio-item">
                            <div class="portfolio-image">
                                <img src="{{ asset($project->image) }}" alt="{{ $project->name }}">
                                <div class="portfolio-overlay">
                                    <div class="portfolio-actions">

                                        <a class="view-portfolio-btn"
                                            href="{{ route('user.profile.show.Project', $project->id) }}">عرض</a>
                                        <form method="GET" action="{{ route('user.projects.edit', $project->id) }}">
                                            <button class="edit-portfolio-btn" data-id="1">تعديل</button>
                                        </form>
                                        <form action="{{ route('user.projects.delete', $project->id) }}" method="POST">
                                            @csrf
                                            <button class="delete-portfolio-btn" data-id="1">حذف</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="portfolio-info">
                                <h4> {{ $project->name }}</h4>
                                <p>{{ $project->description }} </p>
                                <p>{{ $project->job->name }} </p>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Portfolio Modal -->
                <div id="portfolio-modal" class="portfolio-modal">
                    <div class="portfolio-modal-content">
                        <span class="close-modal">&times;</span>
                        <div class="portfolio-modal-body">
                            <div class="portfolio-modal-image">
                                <img id="modal-image" src="" alt="">
                            </div>
                            <div class="portfolio-modal-details">
                                <h3 id="modal-title"></h3>
                                <p id="modal-description"></p>
                                <div class="portfolio-meta">
                                    <div class="meta-item">
                                        <span class="meta-label">التاريخ:</span>
                                        <span id="modal-date"></span>
                                    </div>
                                    <div class="meta-item">
                                        <span class="meta-label">العميل:</span>
                                        <span id="modal-client"></span>
                                    </div>
                                    <div class="meta-item">
                                        <span class="meta-label">الفئة:</span>
                                        <span id="modal-category"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tab-content hidden" id="projects-tab">
                <div class="empty-state">
                    @empty($works)
                        <div class="empty-icon" style="text-align: right">📁</div>
                        <h3>لا توجد مشاريع حالية</h3>
                        <p>لم تقم بإضافة أي مشاريع بعد.</p>
                    @endempty
                    <div id="app">

                        @foreach ($works as $work)
                            <div class="portfolio-info" style="border-bottom: 1px solid #33333324;">
                                <h4>{{ $work->name }} </h4>
                                <p> {{ $work->description }} </p>
                                @if ($work->status == 1)
                                    <span>الحاله :</span> مفتوح
                                @elseif($work->status == 3)
                                    <span>الحاله :</span> مكتمل
                                @else
                                    <span>الحاله :</span> مغلق
                                @endif
                                <br>
                                <span>تاريخ النشر :</span>

                                <time-ago
                                    time="{{ \Carbon\Carbon::parse($work->created_at)->toIso8601String() }}"></time-ago>
                                @if ($work->status == 1)
                                    <div class="control">
                                        <a class="btn btn-primary" href="{{ route('user.submitting', $work->id) }}"
                                            style="  background-color: #1877F2 !important;">تقديم علي الوظيفة</a>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>

                </div>
            </div>

            <div class="tab-content hidden" id="reviews-tab">
                @empty($inboxs)
                    <div class="empty-state">
                        <div class="empty-icon">⭐</div>
                        <h3>صندوق الوارد</h3>
                        <p>لم تحصل على أي تقييمات بعد.</p>
                    </div>
                @endempty
                <div>
                    <div id="app">
                        <div class="inbox">
                            <!-- محادثة 1 -->

                            @foreach ($inboxs as $inbox)
                                <div class="chat-item">
                                    <img src="{{ asset($inbox->avater) }}" class="avatar" alt="user">
                                    <div class="chat-info">
                                        <a href="{{ route('user.message', $inbox->user_id) }}">
                                            <div class="chat-header">
                                                <span class="chat-name"> {{ $inbox->name }}</span>
                                                <span class="chat-time"> <time-ago
                                                        time="{{ \Carbon\Carbon::parse($inbox->last_time)->toIso8601String() }}"></time-ago></span>
                                            </div>
                                            <div class="chat-message"> {{ $inbox->last_message }}</div>
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <div class="profile-actions">
                <a href="{{ route('user.freelanceDashboard') }}" class="edit-profile-btn"> لوحة التحكم </a>
            </div>
            <div class="profile-actions">
                <a href="{{ route('user.setting') }}" class="edit-profile-btn"> الاعدادات </a>
            </div>
        </div>
    </main>


@section('scriptFile')
    <script src="{{ asset('front/asset/js/profile.js') }}"></script>
@endsection
@endsection

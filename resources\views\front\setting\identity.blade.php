@extends('front.layout.layout')

@section('body')

 @include('include.alert') 
 
    <main class="container">
        <div class="verification-container">
            <div class="verification-header">
                 <div class="step-indicator">
                    <div class="step completed">1</div>
                    <div class="step-line"></div>
                    <div class="step completed ">2</div>
                    <div class="step-line"></div>
                    <div class="step completed">3</div>
                    <div class="step-line"></div>
                    <div class="step completed">4</div>
                    <div class="step-line"></div>
                    <div class="step active">5</div>
                </div>
                <h2>التحقق من الهوية</h2>
                <p class="verification-description">لزيادة فرصك في الحصول على عمل، يرجى تحميل إثبات هوية صالح.</p>
                <div class="verification-badge">
                    <div class="badge-icon">✓</div>
                    <p>الحسابات ذات الهوية المُتحقق منها تحظى بفرص أفضل في عملية التوظيف.</p>
                </div>
                <br>
                @if (! Auth::guard()->user()->identity == null)
                <div class="verification-badge">
                    <div class="badge-icon">✓</div>
                    <p>لقد تم استلام طلب الان المراجعة </p>
                </div>
                @endif
            </div>
             @if ( Auth::guard()->user()->identity == null)
            <div class="verification-steps">
                <div class="verification-step">
                    <div class="step-number">1</div>
                    <h3>اختر نوع الوثيقة</h3>
                    <div class="document-types">
                        <div class="document-type-option">
                            <input type="radio" id="id-card" name="document-type" value="id-card" checked>
                            <label for="id-card">
                                <div class="document-icon">🪪</div>
                                <span>بطاقة الهوية الوطنية</span>
                            </label>
                        </div>
                        <div class="document-type-option">
                            <input type="radio" id="passport" name="document-type" value="passport">
                            <label for="passport">
                                <div class="document-icon">📔</div>
                                <span>جواز السفر</span>
                            </label>
                        </div>
                        <div class="document-type-option">
                            <input type="radio" id="driving-license" name="document-type" value="driving-license">
                            <label for="driving-license">
                                <div class="document-icon">🚗</div>
                                <span>رخصة القيادة</span>
                            </label>
                        </div>
                    </div>
                </div>
                <form action="{{route('user.identity.verification')}}" method="POST" enctype="multipart/form-data">
                    @csrf
                
                <div class="verification-step">
                    <div class="step-number">2</div>
                    <h3>تحميل صورة الوثيقة</h3>
                    <div class="document-upload">
                        <div class="upload-side">
                            <h4>الوجه الأمامي</h4>
                            <div class="upload-area" id="front-upload-area">
                                <div class="upload-placeholder" id="front-placeholder">
                                    <div class="upload-icon">📄</div>
                                    <p>اسحب الملف هنا أو انقر للتحميل</p>
                                </div>
                                <div class="upload-preview" id="front-preview"></div>
                                <input name="identity" type="file" id="front-document" accept="image/*" hidden>
                            </div>
                            <button class="upload-button" id="front-upload-btn">اختيار ملف</button>
                        </div>
                    </div>
                    
                    <div class="upload-requirements">
                        <h4>متطلبات الصورة:</h4>
                        <ul>
                            <li>يجب أن تكون الصورة واضحة وغير مشوشة</li>
                            <li>يجب أن تكون جميع المعلومات مقروءة</li>
                            <li>يجب أن تكون بصيغة JPG أو PNG</li>
                            <li>الحد الأقصى للحجم: 5 ميجابايت</li>
                        </ul>
                    </div>
                </div>
                
                <div class="verification-step">
                    <div class="step-number">3</div>
                    <h3>التأكيد والموافقة</h3>
                    <div class="verification-consent">
                        <label class="consent-checkbox">
                            <input type="checkbox" id="consent-checkbox" required>
                            <span>أؤكد أن المعلومات المقدمة صحيحة وكاملة، وأوافق على معالجة بياناتي الشخصية وفقًا <a href="#">لسياسة الخصوصية</a>.</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="verification-actions">

                    <button id="submit-verification" class="primary-button" >إرسال للتحقق</button>
                </form>
            </div>
                 @endif
        </div>

    </main>


    @section('scriptFile')
        <script src="{{asset('front/asset/js/identity.js')}}"></script>
    @endsection
@endsection
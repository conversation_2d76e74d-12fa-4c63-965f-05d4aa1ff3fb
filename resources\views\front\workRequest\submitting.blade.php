@extends('front.layout.layout')

@section('body')

 @include('include.alert')


<head>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body>


<div class="container">
    <div id="app">
    <div class="proposal-card">
        <h2 class="section-title">تقديم عرض على الوظيفة</h2>
        <form id="proposalForm" action="{{route('user.delivery',$ID)}}" method="post">
            @csrf
            <div class="form-group">
                <label>تفاصيل الوظيفة</label>
                <div class="job-details-box">
                    <h3 class="job-title">{{$work->name}}</h3>

                    <div class="job-meta">
                        <span class="job-meta-item">
                            <i class="fas fa-money-bill-wave"></i>
                            الميزانية: {{$work->amount}}
                        </span>
                        <span class="job-meta-item">
                            <i class="fas fa-clock"></i>
                           تاريخ النشر :<time-ago time="{{ $work->created_at->toIso8601String() }}"></time-ago>

                        </span>
                         <span class="job-meta-item">
                            <i class="fas fa-briefcase"></i>
                            موعد التسليم :   {{$work->expiration_date}}
                        </span>
                        <span class="job-meta-item">
                            <i class="fas fa-map-marker-alt"></i>
                            العمل عن بعد
                        </span>
                    </div>

                    <div class="job-description">
                        <p>{{$work->description}}</p>
                    </div>
                    </div>
                </div>

            <div class="form-group">
                <label>السعر المطلوب (بالجنية)</label>
                <div class="price-container">
                    <span>$</span>
                    <input type="number" name="price" placeholder="مثال: 500" required>
                </div>
            </div>

            <div class="form-group">
                <label>مدة التنفيذ (بالأيام)</label>
                <input type="number" name="delivery_time" placeholder="مثال: 7 أيام" required>
            </div>

            <div class="form-group">
                <label>تفاصيل العرض</label>
                <textarea name="proposal_details" name="message" placeholder="اشرح فكرتك، خطتك، وماذا يميز عرضك..." required></textarea>
            </div>

            <button type="submit" class="submit-btn">إرسال العرض</button>
        </form>
    </div>

    <div class="submitted-proposals">
        <h2 class="section-title">العروض المقدمة</h2>

        <div class="proposals-list" id="proposalsList">

            @if ($proposals->isEmpty())
                 <span class="empty">لا توجد عروض اضافة اول عرض</span>

            @else
                  @foreach ($proposals as $proposal)
            <!-- العرض الأول -->
            <div class="proposal-item">
                <div class="proposal-header">
                    <span class="proposal-price">{{$proposal->amount}}</span>
                    <span class="proposal-time">مدة التنفيذ: {{$proposal->DeliveryDate}}يوم</span>
                </div>
                <div class="proposal-details">
                   {{$proposal->message}}
                </div>
                <div class="proposal-user">
                    <div class="user-avatar">م</div>

                    <span>مقدم من:     <a href="{{route('user.profile.Visit.user',$proposal->freelance_id)}}"> {{$proposal->name}}</a> - <time-ago time="{{ $proposal->created_at->toIso8601String() }}"></time-ago></span>
                </div>
            </div>
             @endforeach
            @endif

             </div>
             <!-- #region -->
            <!-- العرض الثاني -->
            {{-- <div class="proposal-item">
                <div class="proposal-header">
                    <span class="proposal-price">$1,200</span>
                    <span class="proposal-time">مدة التنفيذ: 7 أيام</span>
                </div>
                <div class="proposal-details">
                    لدي خبرة 5 سنوات في تصميم المواقع السياحية. سأقدم لك موقعًا بووردبريس مع تخصيص كامل للقالب،
                    يتضمن نظام حجز برحلات، مع إمكانية الدفع عبر البطاقات الإئتمانية وبيبال.
                    سأوفر أيضًا نظام إدارة محتوى سهل الاستخدام لك لإدارة المحتوى بسهولة.
                </div>
                <div class="proposal-user">
                    <div class="user-avatar">س</div>
                    <span>مقدم من: سارة محمد - منذ 5 ساعات</span>
                </div>
            </div> --}}
        </div>
    </div>
</div>
</div>

    @section('scriptFile')
        <script src="{{asset('front/asset/js/menu-toggle.js')}}"></script>
    @endsection
@endsection

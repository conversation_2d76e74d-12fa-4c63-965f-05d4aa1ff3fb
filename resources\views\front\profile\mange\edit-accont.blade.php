@extends('front.layout.layout')

@section('body')

 @include('include.alert') 
     @vite(['resources/css/app.css', 'resources/js/app.js']) 

     <main class="container">
        <div class="profile-overview">
            
            <div class="profile-name-container">
                <h4 id="job" style="color: #333; ">  {{Auth::guard()->user()->name}}</h4>
            </div>
            
            <div class="profile-tabs">
                <button class="tab-button active" data-tab="info">تعديل معلومات الحساب</button>
           
            </div>
            
            <div class="tab-content" id="info-tab">
                <div class="profile-info-section">
                   
             <div id="editaccount">
                 
                 <form action="{{route('user.profile.update')}}" method="POST" >
                     @csrf
                     <editaccount-component></editaccount-component>
                    <div class="profile-actions">
                        <button  class="edit-profile-btn">حفظ  </button>
                    </div>
                    </form> 
                  </div>

                </div>
            </div>                          
        </div>
    </main>

    @section('scriptFile')
        <script src="{{asset('front/asset/js/menu-toggle.js')}}"></script>
    @endsection
@endsection
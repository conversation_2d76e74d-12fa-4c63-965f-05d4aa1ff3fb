<?php

namespace App\Http\Controllers\front;

use App\Http\Controllers\Controller;
use App\Models\front\Proposal;
use App\Models\front\Work;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SubmittingController extends Controller
{
    //

    public function index ($id) {


        $ID = $id;

        $work = Work::where('id',$id)->first();

        $proposals = Proposal::where('work_id', $id)

        ->join('users','users.id','=','freelance_id')

        ->select('proposals.created_at','message','DeliveryDate','amount','work_id','freelance_id','name')

        ->latest('proposals.created_at')->paginate(10);

          return view("front.workRequest.submitting", compact("work",'ID','proposals') );
    }

    public function submitting(Request $request, $id) {


        $work = Work::find($id);

        $user = Proposal::where('work_id',$id)->where('freelance_id',Auth::user()->id)->first();

        // return   $user ;


        if($user){

            return redirect()->back()->with('error','تم تقديم طلبك مسبقا');
        }

        if( $work->status == 0) {
             return redirect()->back()->with('error','الوظيفة مغلقة');

        }

        $proposal = Proposal::create([
          'work_id' =>$id,
          'user_id' => $work->user_id,
          'freelance_id' => Auth::user()->id,
          'categorie_id' => Auth::user()->categorie_id,
          'amount' =>$request->price,
          'message' =>$request->proposal_details,
          'DeliveryDate' =>$request->delivery_time,
          'status' => 1,
          'created_at' => NOW(),
          'updated_at' => NOW()
        ]);

        if( $proposal) {
            return redirect()->back()->with('success','تم ضافة عرضك بنجاح');

        }
        return redirect()->back()->with('error','حدث خطاء ما');

    }
}

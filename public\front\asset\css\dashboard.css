        /* Dashboard Styles */
        .dashboard-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .dashboard-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .dashboard-header h2 {
            color: var(--main-color);
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
        }

        .dashboard-header p {
            color: #666;
            font-size: 1.1rem;
        }

        /* Statistics Section */
        .stats-section {
            margin-bottom: 3rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-card.total {
            border-right: 4px solid #1877F2;
        }

        .stat-card.completed {
            border-right: 4px solid #28a745;
        }

        .stat-card.in-progress {
            border-right: 4px solid #ffc107;
        }

        .stat-card.open {
            border-right: 4px solid #17a2b8;
        }

        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }

        .stat-info h3 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.3rem;
            color: #333;
        }

        .stat-info p {
            color: #666;
            font-size: 0.95rem;
            margin: 0;
        }

        /* Projects Section */
        .projects-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .tabs-container {
            width: 100%;
        }

        .tab-buttons {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .tab-button {
            flex: 1;
            padding: 1rem 1.5rem;
            border: none;
            background: transparent;
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
            font-weight: 600;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .tab-button:hover {
            background: rgba(24, 119, 242, 0.1);
            color: var(--main-color);
        }

        .tab-button.active {
            background: white;
            color: var(--main-color);
            border-bottom: 3px solid var(--main-color);
        }

        .tab-count {
            background: var(--main-color);
            color: white;
            font-size: 0.8rem;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            min-width: 20px;
            text-align: center;
        }

        .tab-button.active .tab-count {
            background: var(--main-color);
        }

        .tab-content {
            display: none;
            padding: 2rem;
        }

        .tab-content.active {
            display: block;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        /* Project Cards */
        .project-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-right: 4px solid transparent;
        }

        .project-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        }

        .project-card.open-project {
            border-right-color: #17a2b8;
        }

        .project-card.progress-project {
            border-right-color: #ffc107;
        }

        .project-card.completed-project {
            border-right-color: #28a745;
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            gap: 1rem;
        }

        .project-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin: 0;
            line-height: 1.4;
        }

        .project-status {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            white-space: nowrap;
        }

        .project-status.open {
            background: #d1ecf1;
            color: #0c5460;
        }

        .project-status.in-progress {
            background: #fff3cd;
            color: #856404;
        }

        .project-status.completed {
            background: #d4edda;
            color: #155724;
        }

        .project-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
        }

        .project-details {
            margin-bottom: 1.5rem;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.4rem 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #555;
            font-size: 0.9rem;
        }

        .detail-value {
            color: #333;
            font-size: 0.9rem;
        }

        /* Progress Section */
        .progress-section {
            margin-bottom: 1.5rem;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .progress-info span {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .progress-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ffc107, #ffca2c);
            transition: width 0.3s ease;
        }

        /* Rating Section */
        .rating-section {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
            padding: 0.8rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .rating-label {
            font-weight: 600;
            color: #555;
            font-size: 0.9rem;
        }

        .rating-stars {
            font-size: 1rem;
        }

        .rating-value {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        /* Project Actions */
        .project-actions {
            display: flex;
            gap: 0.8rem;
            flex-wrap: wrap;
        }

        .project-actions button {
            flex: 1;
            min-width: 120px;
            padding: 0.6rem 1rem;
            border-radius: 6px;
            font-family: 'Cairo', sans-serif;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
        }

        .primary-button {
            background: var(--main-color);
            color: white;
        }

        .primary-button:hover {
            background: #1565C0;
            transform: translateY(-1px);
        }

        .secondary-button {
            background: #6c757d;
            color: white;
        }

        .secondary-button:hover {
            background: #545b62;
            transform: translateY(-1px);
        }

        .outline-button {
            background: transparent;
            color: var(--main-color);
            border: 2px solid var(--main-color);
        }

        .outline-button:hover {
            background: var(--main-color);
            color: white;
            transform: translateY(-1px);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-icon {
                font-size: 2rem;
            }

            .stat-info h3 {
                font-size: 1.5rem;
            }

            .tab-buttons {
                flex-direction: column;
            }

            .tab-button {
                padding: 0.8rem 1rem;
            }

            .projects-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .project-card {
                padding: 1rem;
            }

            .project-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .project-actions {
                flex-direction: column;
            }

            .project-actions button {
                min-width: auto;
            }

            .tab-content {
                padding: 1rem;
            }
        }

        @media (max-width: 480px) {
            .dashboard-container {
                padding: 0 0.5rem;
            }

            .dashboard-header h2 {
                font-size: 1.8rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .stat-card {
                flex-direction: column;
                text-align: center;
                gap: 0.5rem;
            }
        }

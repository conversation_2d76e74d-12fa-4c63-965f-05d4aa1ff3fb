<template>
  <div>
       <select v-model="job_id" @change="getJobs" name="s1">
        <option selected value="0">slect</option>
         <option v-for="categories in categorie" :key="categories.id" :value="categories.id">{{  categories.name}}</option>
      </select> 

       <select name="s2">
         <option v-for="categories in jobs" :key="categories.id" :value="categories.id">{{  categories.name}}</option>
      </select> 
  </div>
</template>

<script>
export default {
 
  data() {
    return {
    categorie : [], 
    job_id : 0,
    jobs :[]

    };
  },
  methods:{
    getJobs () {
          fetch('http://127.0.0.1:8000/get-jobs/' +this.job_id)
        .then(res => res.json())
        .then(data => this.jobs = data) 
        .catch(err =>console.log(err.message));
       
    }
  },
 mounted (){
    fetch('http://127.0.0.1:8000/categorie')
        .then(res => res.json())
        .then(data => this.categorie = data) 
        .catch(err =>console.log(err.message));
  },

}
</script>

<style>

</style>
<template>
  <div>
    <div v-for="post in posts" :key="post.id" class="p-3 mb-2 border rounded">

     <div class="faq-item">
          <a :href="`Visit/user/${post.freelance_id}`" class="faq-question" >{{ post.name }}</a>
          <a :href="`Proposal/${post.id}}`"><p class="faq-answer">{{ post.message }}</p></a>
          <a :href="`Proposal/${post.id}`"> <small class="faq-question">استفسار حول : {{ (post.workTitel) }}</small></a>

     </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "PostsComponent",
  props: {
    number : Number
  },
  data() {
    return {
      interval: null,
      posts : []
    };
  },
  methods: {
    async fetchPosts() {

       fetch('http://127.0.0.1:8000/posts/'+this.number)
        .then(res => res.json())
        .then(data => this.posts = data)
        .catch(err =>console.log(err.message));
    },
    formatTime(time) {
      const date = new Date(time);
      return date.toLocaleString(); // لاحقاً ممكن نستخدم timeago.js
    },
  },
  mounted() {
    this.fetchPosts();
    this.interval = setInterval(this.fetchPosts, 10000); // كل 10 ثواني
  },
  beforeUnmount() {
    clearInterval(this.interval);
  },
};
</script>

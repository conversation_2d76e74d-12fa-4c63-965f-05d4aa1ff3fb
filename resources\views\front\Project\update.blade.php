@extends('front.layout.layout')

@section('body')

 @include('include.alert') 
   <style>
 
.profile-icon{
    position: absolute;
    z-index: 1;
    font-size: 23px;
    color: var(--main-color);
    cursor: pointer;
    } 
 .img{
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #1877F2;
    margin-bottom: 1rem;
    position: relative;
     }
    .image-upload-container {
      border: 2px dashed #ddd;
      border-radius: 10px;
      padding: 2rem;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 1.5rem;
    }
    
    .image-upload-container:hover {
      border-color: var(--accent-color);
      background-color: rgba(72, 149, 239, 0.05);
    }
    
    .upload-icon {
      font-size: 3rem;
      color: var(--accent-color);
      margin-bottom: 1rem;
    }
    
    .image-preview {
      max-width: 100%;
      max-height: 300px;
      border-radius: 10px;
      display: none;
      margin: 0 auto 1.5rem;
    }
    
    .btn-submit {
      background-color: var(--success-color);
      color: white;
      border: none;
      padding: 0.8rem 2rem;
      border-radius: 10px;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: 0 5px 15px rgba(76, 201, 240, 0.3);
      width: 100%;
      margin-top: 1rem;
    }
    
    .btn-submit:hover {
      background-color: #3ab7d8;
      transform: translateY(-3px);
      box-shadow: 0 8px 20px rgba(76, 201, 240, 0.4);
    }
    
    .btn-cancel {
      background-color: #f1f1f1;
      color: #666;
      border: none;
      padding: 0.8rem 2rem;
      border-radius: 10px;
      font-weight: 600;
      transition: all 0.3s ease;
      width: 100%;
      margin-top: 1rem;
    }
    
  </style>
     <main class="container">
        <div class="profile-overview">
            
            <div class="profile-name-container">
                <h4 id="job" style="color: #333; ">  المشاريع</h4>
            </div>
            
            <div class="profile-tabs">
                <button class="tab-button active" data-tab="info">تعديل المشروع   </button>
           
            </div>
            
            <div class="tab-content" id="info-tab">
                <div class="profile-info-section">
                    <form action="{{route('user.projects.update',$project->id)}}" method="POST" enctype="multipart/form-data">
                        @csrf
                    <h3>معلومات المشروع</h3>
                    <div class="info-item-edit">
                         <div class="info-label"> اسم المشروع  </div>
                        <div class="info-value" id="user-email"><input type="text" name="name" value="{{$project->name}}"></div> 
                    </div>
                    <div class="info-item-edit">
                         <div class="info-label">وصف  المشروع  </div>
                        <div class="info-value" id="user-email"><textarea style="height: 300px" type="text" name="description" >
                           {{ $project->description}}
                        </textarea></div> 
                    </div>
      
            <!-- اختيار القسم -->
            <div class="form-group">
                <label for="department">اختر القسم</label>
                <select id="department" name="department_id" required>
                    <option value="{{$project->category->id}}">  {{$project->category->name}}</option>
                    @foreach($departments as $department)
                        <option value="{{ $department->id }}">{{ $department->name }}</option>
                    @endforeach
                </select>
            </div>

          <div class="mb-4">
              <label class="form-label">صورة المنشور الرئيسية</label>
              <div class="image-upload-container" id="imageUpload">
                <div class="upload-icon">
                     <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <h5>اسحب وأسقط الصورة هنا أو انقر للاختيار</h5>
                <p class="text-muted">الصور بدقة 1200x630 بكسل أو أكبر</p>
            </div>
            <img id="imagePreview"class="image-preview" alt="معاينة الصورة">
        </div>
        <input type="file"  name="img"  id="imageInput" accept="image/*" style="display: none;" >
 


                    <div class="profile-actions">
                        <button  class="edit-profile-btn">حفظ  </button>
                    </div>
                    </form> 

               </div>
            </div>     
                                     
        </div>
    </main> 


   
<script>
  // معاينة الصورة
  const imageUpload = document.getElementById('imageUpload');
  const imageInput = document.getElementById('imageInput');
  const imagePreview = document.getElementById('imagePreview');
  
  imageUpload.addEventListener('click', () => {
    imageInput.click();
  });
  
  imageInput.addEventListener('change', (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        imagePreview.src = event.target.result;
        imagePreview.style.display = 'block';
        imageUpload.style.display = 'none';
      };

      reader.readAsDataURL(file);
    }
  });
  
</script>


    @section('scriptFile')
        <script src="{{asset('front/asset/js/menu-toggle.js')}}"></script>
    @endsection
@endsection 
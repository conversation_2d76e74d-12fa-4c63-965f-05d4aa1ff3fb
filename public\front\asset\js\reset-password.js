
// Toggle menu for mobile
const toggleBtn = document.querySelector('.menu-toggle');
const nav = document.querySelector('.main-nav');

toggleBtn.addEventListener('click', () => {
    nav.classList.toggle('active');
});

// Password strength indicator
const passwordInput = document.getElementById('password');
const strengthIndicator = document.getElementById('password-strength');

passwordInput.addEventListener('input', function () {
    const password = this.value;
    let strength = 0;
    let feedback = '';

    if (password.length >= 8) strength += 1;
    if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength += 1;
    if (password.match(/\d/)) strength += 1;
    if (password.match(/[^a-zA-Z\d]/)) strength += 1;

    switch (strength) {
        case 0:
            feedback = 'ضعيفة جدًا';
            strengthIndicator.className = 'password-strength very-weak';
            break;
        case 1:
            feedback = 'ضعيفة';
            strengthIndicator.className = 'password-strength weak';
            break;
        case 2:
            feedback = 'متوسطة';
            strengthIndicator.className = 'password-strength medium';
            break;
        case 3:
            feedback = 'قوية';
            strengthIndicator.className = 'password-strength strong';
            break;
        case 4:
            feedback = 'قوية جدًا';
            strengthIndicator.className = 'password-strength very-strong';
            break;
    }

    strengthIndicator.textContent = `قوة كلمة المرور: ${feedback}`;
});

// Form submission

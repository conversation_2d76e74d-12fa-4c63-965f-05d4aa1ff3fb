@extends('front.layout.layout')

@section('body')

 @include('include.alert')

     <main class="container">
        <div class="profile-overview">

            <div class="profile-name-container">
                <h4 id="job" style="color: #333; ">  المشاريع</h4>
            </div>

            <div class="profile-tabs">
                <button class="tab-button active" data-tab="info">تعديل المشروع   </button>

            </div>

            <div class="tab-content" id="info-tab">
                <div class="profile-info-section">
                    <form action="{{route('user.work.update',$work->id)}}" method="POST" enctype="multipart/form-data">
                        @csrf
                    <h3>معلومات المشروع</h3>
                    <div class="info-item-edit">
                         <div class="info-label">  العنوان  </div>
                        <div class="info-value" id="user-email"><input placeholder="مثلا عامل تبريد و تكيف" type="text" name="name" value="{{$work->name}}"></div>
                    </div>
                    <div class="info-item-edit">
                         <div class="info-label">وصف  المشروع  </div>
                        <div class="info-value" id="user-email"><textarea type="text" name="description" style="height: 300px">{{$work->description}}</textarea></div>
                    </div>
                    <div class="info-item-edit">
                         <div class="info-label">  المكان  </div>
                        <div class="info-value" id="user-email"><input placeholder=" موقع العمل" type="text" name="place" value="{{$work->place}}"></div>
                    </div>
                    <div class="info-item-edit">
                         <div class="info-label">  المكان  </div>
                        <div class="info-value" id="user-email">
                            <select name="status">الحاله
                                 <option value="{{$work->status}}"> @if ($work->status == 1) مفتوح@else مغلق @endif</option>
                                 <option value="1">مفتوح</option>
                                 <option value="0">مغلق</option>
                            </select>
                            <input placeholder=" موقع العمل" type="text" name="place" value="{{$work->place}}"></div>
                    </div>
            <!-- اختيار القسم -->
                <div class="info-item-edit">
                     <label for="department">اختر القسم</label>
                     <select id="department" name="department_id" required>
                          <option value="{{$work->categoryWork->id}}"> {{$work->categoryWork->name}}</option>
                          @foreach($departments as $department)
                          <option value="{{ $department->id }}">{{ $department->name }}</option>
                    @endforeach
                    </select>
                </div>
                <div class="info-item-edit">
                    <div class="info-label">   الميزانية بالجنية السوداني </div>
                        <div class="info-value" id="user-email"><input placeholder=" مثلا 10000ج" type="number" name="amount" value="{{$work->amount}}"></div>
                    </div>
                <div class="info-item-edit">
                    <div class="info-label"> ايام التسليم </div>
                        <div class="info-value" id="user-email"><input placeholder=" عدد ايام التليم " type="text" name="expiration_date" value="{{$work->expiration_date}}"></div>
                    </div>
                <div class="profile-actions">
                        <button  class="edit-profile-btn">حفظ  </button>
                    </div>
                    </form>

               </div>
            </div>

        </div>
    </main>




    @section('scriptFile')
        <script src="{{asset('front/asset/js/menu-toggle.js')}}"></script>
    @endsection
@endsection

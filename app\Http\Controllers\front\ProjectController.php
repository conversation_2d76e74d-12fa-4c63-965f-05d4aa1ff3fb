<?php

namespace App\Http\Controllers\front;

use App\Helpers\uplodeImage;
use App\Http\Controllers\Controller;
use App\Http\Requests\front\porfile\ProjectRequest;
use App\Models\front\Categorie;
use App\Models\front\Project;
use App\Models\front\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;

class ProjectController extends Controller
{

    public function index()
    {
        return 'index';
    }

    public function display()
    {
        return 'display';
    }
    public function create()
    {

        $departments = Categorie::select('id', 'name')->get();


        return view('front.Project.create', compact('departments'));
    }

    public function save(ProjectRequest $request)
    {


        $user = User::select()->where('id', Auth::user()->id)->first();


        if ($user->project_count >= 10) {

            return Redirect()->back()->with('error',  'الحد الاعلي للمشاريع 10 فقد ');
        }

        $user->update(['project_count' => $user->project_count + 1]);


        if (!empty($request->img)) {

            $imge = uplodeImage::uplode($request->img);



            $Project = Project::create([
                'name' => $request->name,
                'description' => $request->description,
                'image' => $imge,
                'user_id' => Auth::user()->id,
                'job_id' => Auth::user()->job_id,
                'category_id' => $request->department_id,
                'updated_at' => NOW(),
                'created_at' => NOW()
            ]);

            return  Redirect()->back()->with('success', 'تم الاضافة بنجاح');
        }
        return Redirect()->back()->with('error', 'حدث خطاء ما الرجاء المحاولة مرة اخره');
    }

    public function edit($id)
    {

        $departments = Categorie::select('id', 'name')->get();

        $project = Project::with(['category', 'job'])->where('id', $id)->where('user_id', Auth::user()->id)->first();


        return view('front.Project.update', compact('departments', 'project'));
    }

    public function update(ProjectRequest $request, $id)
    {

        $project = Project::where('id', $id)->first();

        if (!empty($request->img)) {

            $imge = uplodeImage::uplode($request->img, 'assets/imgs/Profile/', $project->image);

            $project->update(['image' =>  $imge]);
        }

        $project->update([
            'name' =>  $request->name,
            'description' =>  $request->description,
            'category_id' =>  $request->department_id,
            'updated_at' =>  Now()
        ]);

        if ($project) {

            return  Redirect()->back()->with('success', 'تم الاضافة بنجاح');
        }

        return Redirect()->back()->with('error', 'حدث خطاء ما الرجاء المحاولة مرة اخره');
    }


    public function delete($id)
    {

        $project = Project::where('id', $id)->first();

        $user = User::select()->where('id', Auth::user()->id)->first();

        if ($user) {
            if ($user->project_count == 0) {

                return Redirect()->back()->with('error', 'حدث خطاء ما الرجاء المحاولة مرة اخره');
            }

            $user->update(['project_count' => $user->project_count - 1]);

            if ($project) {

                if (!empty($project->image)) {

                    File::delete(public_path($project->image));
                }

                $project->delete();

                return  Redirect()->route('user.profile.index')->with('success', 'تم الحذف بنجاح');
            }
        }
    }

    public function back()
    {

        return redirect()->route('user.profile.index');
    }
}

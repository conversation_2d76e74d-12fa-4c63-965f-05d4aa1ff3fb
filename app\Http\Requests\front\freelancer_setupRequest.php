<?php

namespace App\Http\Requests\front;

use Illuminate\Foundation\Http\FormRequest;

class freelancer_setupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

     public function rules()
    {
        return [
            'experince' => 'required|numeric|max:100',
            'bio' => 'required|string|max:600',
            'address' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'skills' => 'required|string',
        ];
    }

    public function messages()
    {
        return [

            'experince.required' => 'الرجاء ادخال   سنوات الخبرة',
            'bio.required' => 'الرجاء ادخال   نبذة مختصرة ',
            'address.required' => 'الرجاء ادخال مكان الاقامة  ',
            'phone.required' => 'الرجاء ادخال رقم الهاتف  ',
            'skills.required' => 'الرجاء ادخال   المهارات  ',

            'bio.max' => 'الحد الاقصي للحروف 600 حرف  نبذة مختصرة',
            'address.max' => 'الحد الاقصي للحروف 255 حرف مكان الاقامة',
            'experince.max' => 'الحد الاقصي للحروف 10 حرف  سنوات الخبرة',
            'phone.max' => 'الحد الاقصي للحروف 255 حرف رقم الهاتف'

        ];
    }
}

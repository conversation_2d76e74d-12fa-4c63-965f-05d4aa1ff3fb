<?php

namespace App\Models\front;

use Illuminate\Database\Eloquent\Model;

class Work extends Model
{
    public $fillable = ['name', 'description', 'image', 'amount', 'place',

    'expiration_date','user_id', 'status','category_id','updated_at', 'created_at'];

    public function userWork()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function categoryWork()
    {
        return $this->belongsTo(Categorie::class, 'category_id');
    }
}

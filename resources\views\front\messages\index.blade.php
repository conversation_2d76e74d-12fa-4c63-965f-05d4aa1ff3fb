@extends('front.layout.layout')

@section('body')

<head>
  <meta charset="UTF-8">
  <meta name="csrf-token" content="{{ csrf_token() }}">
  @vite(['resources/css/app.css', 'resources/js/app.js'])

</head>

    @include('include.alert')
    <span style="display: block; margin-top:30px "></span>
    <div class="inbox" style="margin-bottom:250px ">
        <!-- محادثة 1 -->

        <div id="app">
            @foreach ($messages as $message)
                <div class="chat-item">
                    <img src="{{ asset($message->avater) }}" class="avatar" alt="user">
                    <div class="chat-info">

                        <div class="chat-header">
                            <span class="chat-name">{{ $message->sender_name }}</span>

                            <span class="chat-time"> <time-ago
                                    time="{{ \Carbon\Carbon::parse($message->created_at)->toIso8601String() }}"></time-ago></span>
                        </div>
                        <div class="chat-message"> {{ $message->message }}</div>
                    </div>
                </div>
            @endforeach
        </div>
        <div id="message">
           <messages-component></messages-component>
            <form action="{{route('user.message.save')}}" method="post">
                @csrf
                <input type="text" name="message" placeholder="اكتب الرسالة هنا ...">
                <input style="display: none" type="" value="{{$receiver}}" name="receiver_id">
                <button>ارسال</button>
            </form>
        </div>
    </div>


@section('scriptFile')
    <script src="{{ asset('front/asset/js/menu-toggle.js') }}"></script>
@endsection
@endsection

@extends('front.layout.layout')

@section('body')

<head>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
       <main class="container">
        <h1 style="margin-top: 50px;">مرحبًا بك في منصة <span style="color:#ff7043;">انجاز</span>  بوابتك للعمل الحر الاحترافي</h1>

        <p class="intro">
            نحن هنا لنوفر لك بيئة متكاملة تجمع بين أصحاب المشاريع والمستقلين المبدعين من كل أنحاء العالم العربي.
            سواء كنت تبحث عن محترف لإنجاز مهامك بسرعة وجودة عالية، أو كنت مستقلًا تسعى لتقديم خدماتك واكتساب عملاء جدد،
            فإن
            <strong>انجاز</strong> هو المكان المناسب لك.
        </p>

        <ul class="features">
            <li>سهولة الاستخدام: تصميم بسيط وسلس يمكن الجميع من إيجاد الفرص أو توظيف المستقلين بسرعة.</li>
            <li>حماية المعاملات: نظام آمن يضمن حقوق الطرفين ويتيح الدفع بعد التأكد من جودة العمل.</li>
            <li>مجتمع واسع: آلاف المشاريع والمهام التي تناسب كل المهارات والمجالات.</li>
            <li>دعم مخصص: فريق دعم متواجد لمساعدتك في أي وقت.</li>
        </ul>

        <a href="#register" class="cta-button">سجّل الآن مجانًا</a>
    </main>


    <!-- آخر المشاريع -->
    <section class="container">
      <h2 class="section-title">آخر المشاريع</h2>

      <div class="projects">
          <div id="app">

     @foreach ($work as $works )

        <div class="project-card">
          <h3 class="project-title">   {{$works ->name}}</h3>
          <p class="project-desc">{{$works ->description}}</p>
          <div class="project-meta">تاريخ النشر: <time-ago time="{{ \Carbon\Carbon::parse($works->created_at)->toIso8601String() }}"></time-ago> | بواسطة: محمد أحمد</div>
        </div>
    @endforeach
      </div>
      </div>
    </section>

        <!-- أفضل المستقلين -->
    <section class="container">
      <h2 class="section-title">أفضل المستقلين</h2>

      <div class="freelancers">
        <div class="freelancer-card">
          <img src="{{asset('assets/imgs/Profile/default.jpg')}}" alt="صورة العامل" class="freelancer-photo" />
          <div class="freelancer-name">أحمد علي <span class="verified-badge">موثّق</span></div>
          <div class="freelancer-rating">★★★★★</div>
          <div class="freelancer-job">مطور ويب - Laravel</div>
        </div>

        <div class="freelancer-card">
          <img src="{{asset('assets/imgs/Profile/default.jpg')}}" alt="صورة العامل" class="freelancer-photo" />
          <div class="freelancer-name">سارة محمد</div>
          <div class="freelancer-rating">★★★★☆</div>
          <div class="freelancer-job">مصممة جرافيك</div>
        </div>
      </div>
    </section>

    <!-- آراء العملاء -->
    <section class="container">
      <h2 class="section-title">آراء العملاء</h2>

      <div class="testimonials">
        <div class="testimonial-card">
          تجربة ممتازة جدًا، حصلت على مصمم محترف أنجز لي مشروعي بسرعة وبجودة عالية. أنصح الجميع باستخدام المنصة.
        </div>
        <div class="testimonial-card">
          المنصة سهلة الاستخدام وفريق الدعم متعاون جدًا. أنجزت مشروعين حتى الآن وكل شيء سار بشكل احترافي.
        </div>
        <div class="testimonial-card">
          أحببت نظام التقييمات، ساعدني كثيرًا في اختيار المستقل المناسب لمشروعي.
        </div>
      </div>
    </section>

    <!-- الأسئلة الشائعة -->
    <section class="container">
      <h2 class="section-title">الأسئلة الشائعة</h2>

      <div class="faq">
        <div class="faq-item">
          <div class="faq-question">كيف أبدأ بنشر مشروعي؟</div>
          <div class="faq-answer">قم بإنشاء حساب مجاني ثم انتقل إلى صفحة "إضافة مشروع" واملأ البيانات المطلوبة.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">كيف يتم الدفع للمستقل؟</div>
          <div class="faq-answer">عند اكتمال المشروع بشكل مرضي يتم تحرير المبلغ من الضمان وتحويله لحساب المستقل.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">هل توجد رسوم استخدام للمنصة؟</div>
          <div class="faq-answer">التسجيل مجاني، ولكن يتم خصم نسبة بسيطة من قيمة المشروع بعد إتمامه كعمولة للمنصة.</div>
        </div>
      </div>
    </section>
    @section('scriptFile')
        <script src="{{asset('front/asset/js/menu-toggle.js')}}"></script>
    @endsection

@endsection




<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <title>البوستات</title>
    @vite('resources/js/app.js')
</head>
<body dir="rtl">

    {{-- <div id="app2" class="container mx-auto p-5">
        <proposals-component :number ="{{Auth::guard()->user()->id}}"></proposals-component>
    </div> --}}
</body>
</html>

<?php

namespace App\Http\Controllers\front;

use App\Http\Controllers\Controller;
use App\Http\Requests\front\LoginReques;
use App\Http\Requests\front\RegisterReques;
use App\Http\Requests\front\TestRequest;
use App\Models\front\User;
use App\Models\front\Work;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Redirect;

class AuthController extends Controller
{

    public function test()
    {

        $posts = Work::latest()->get();
        return  json_decode($posts); // view('test',compact('posts'));
    }

    public function save(TestRequest $request)
    {
        return $request;
    }

    public function index()
    {

        if (Auth::user() && !Auth::user()->categorie_id == null) {


        $work = DB::table('works as p')
            ->join('categories as fc', 'p.category_id', '=', 'fc.id')
            ->select(
                'p.*',
                DB::raw("
              (
                CASE
                    WHEN p.created_at >= NOW() - INTERVAL 7 DAY THEN 50
                    WHEN p.created_at >= NOW() - INTERVAL 30 DAY THEN 25
                    ELSE 0
                END
                +
                CASE WHEN fc.id IS NOT NULL THEN 50 ELSE 0 END
            ) AS score
        ")
            )
            ->where('fc.id',     Auth::user()->categorie_id)
            //  ->where('p.status', 1)
            ->orderByDesc('score')
            ->orderByDesc('p.created_at')
            ->get(6);

        } else {

        $work = DB::table('works as p')
            ->join('categories as fc', 'p.category_id', '=', 'fc.id')
            ->select(
                'p.*',
                DB::raw("
              (
                CASE
                    WHEN p.created_at >= NOW() - INTERVAL 7 DAY THEN 50
                    WHEN p.created_at >= NOW() - INTERVAL 30 DAY THEN 25
                    ELSE 0
                END
                +
                CASE WHEN fc.id IS NOT NULL THEN 50 ELSE 0 END
            ) AS score
        ")
            )
            ->orderByDesc('score')
            ->orderByDesc('p.created_at')
            ->paginate(6);

        }



        return    view('welcome', compact('work'));
    }

    public function login()
    {

        return view('front.Auth.login');
    }

    public function register()
    {

        return view('front.Auth.register');
    }

    public function check_login(LoginReques $request)
    {

        $user = User::where('email', $request->email)->first();

        if (!$user) {

            return Redirect()->back()->with('error', 'البريد الالكتروني او كلمة المرور غير صحيحة');
        }
        if ($user->verification_email == 0) {

            Auth::login($user);

            return   redirect()->route('user.verification');
        }


        if (Hash::check($request->password, $user->password)) {

            Auth::login($user);

            if (Auth::user()->account_type) {

                return redirect()->route('user.profile.index');
            }

            return redirect()->route('user.profile.user.index');
        }

        return Redirect()->back()->with('error', 'البريد الالكتروني او كلمة المرور غير صحيحة');
    }

    public function check_register(RegisterReques $request)
    {
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'job_id' => 1,
            'avater' => 'assets/imgs/Profile/default.jpg',
            'password' => Hash::make($request->password),
            'updated_at' => NOW(),
            'created_at' => NOW()
        ]);

        if ($user) {

            Auth::login($user);

            return Redirect()->route('user.verification');
        } else {

            return Redirect()->back()->with('error', 'حدث خطاء ما حاول مرة ثانية');
        }
    }

    public function verification()
    {

        $randomcode = rand(100000, 999999);

        $user = User::select()->where('id', Auth::guard()->user()->id)->first();

        $user->update([
            'code' => $randomcode,
        ]);

        return view('front.Auth.verification-email');
    }

    public function tryAgan()
    {

        return view('front.Auth.verification-email');
    }

    public function check_verification(Request $request)
    {



        $user = User::where('code', $request->code)
            ->where('id', Auth::id())
            ->first();


        if ($user) {

            $user->update([
                'verification_email' => '1',
            ]);

            return Redirect()->route('user.success_verification');
        }

        return Redirect()->route('user.tryAgan_verification')->with('error', 'الكود غير صحيح');
    }

    public function success_verification()
    {

        return view('front.Auth.email-verified-success');
    }

    public function account_type()
    {
        return view('front.Auth.account-type');
    }

    public function photo_index()
    {

        return view('front.Auth.upload-photo');
    }

    public function forgot()
    {
        return view('front.Auth.forgot-password');
    }

    public function forgot_send(Request $request)
    {


        $pass = $request->validate(['email' => 'required|email']);

        if (!$pass) {

            return Redirect()->back()->with('error', 'الرجاء ادخال البريد الالكتروني بشكل صحيح');
        }

        $user = User::where('email', $request->email)->first();

        if (!$user) {

            return Redirect()->back()->with('error', 'البريد الالكتروني غير مسجل');
        }

        $randomcode = rand(100000, 999999);

        $user->update([
            'code' => $randomcode,
        ]);


        $_SESSION['forgotPassword'] = $user;



        return view('front.Auth.reset-password-sent');
    }

    public function forgot_code(Request $request)
    {


        $user = User::where('code', $request->code)
            ->where('id',  $_SESSION['forgotPassword']['id'])
            ->first();


        if ($user) {

            return redirect()->route('user.chanc.password');
        }

        return Redirect()->route('user.tryAgan_forget')->with('error', 'الكود غير صحيح');
    }

    public function forgot_tryagan()
    {

        return view('front.Auth.reset-password-sent');
    }

    public function chanc_password()
    {

        return view('front.Auth.reset-password');
    }

    public function save_password(Request $request)
    {

        $pass = $request->validate([
            'password' => 'required|same:password_confirmation|min:8'
        ]);

        if (!$pass) {
            return Redirect()->back()->with('error', 'يجب ان تكون كلمة المرور مكونة من 8 احرف');
        }

        $user = User::where('id',  $_SESSION['forgotPassword']['id'])->first();

        $user->update([
            'pssword' => Hash::make($request->password),
        ]);

        Auth::login($user);

        if (Auth::user()->account_type) {

            return  Redirect()->route('user.profile.index')->with('success', 'تم التحديث بنجاح');
        }

        return redirect()->route('user.profile.user.index')->with('success', 'تم التحديث بنجاح');
    }

    public function logout(Request $request)
    {

        Auth::logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return Redirect()->route('login');
    }
}

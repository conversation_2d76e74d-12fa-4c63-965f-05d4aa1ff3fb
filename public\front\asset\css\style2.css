/* ================================
   إعداد المتغيرات العامة (ألوان وخطوط)
================================== */
:root {
    --main-color: #1e90ff;
    /* اللون الأساسي */
    --secondary-color: #ff7043;
    /* الخلفية الداكنة */
    --text-color: #333;
    /* لون النص */
    --accent-color: #38bdf8;
    /* لون مميز */
    --danger-color: #ef4444;
    /* لون التحذيرات */
    --font-main: 'Cairo', sans-serif;
    /* خط عربي جميل */
    --red-color: #f44336;
    --orang-color: #f59e0b;
    --grenn-color: #22c55e;
}

/* ================================
   إعدادات أساسية
================================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

a {
    text-decoration: none;
}

ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

html {
    direction: rtl;
    /* ⚠️ مهم لمواقع عربية */
}

body {
    font-family: var(--font-main);
    background-color: white;
    color: var(--text-color);
    line-height: 1.8;
    font-family: 'Cairo', sans-serif;
    font-weight: 400;
    /* الوزن الافتراضي */
    direction: rtl;
}

/* ================================
   ترويسة الموقع
================================== */
header {
    background-color: var(--main-color);
    padding: 1rem 2rem;
    text-align: center;
    color: #fff;
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 2rem;
}

nav a {
    color: #fff;
    text-decoration: none;
    font-weight: bold;
}

/* ================================
   الأقسام العامة
================================== */
.section {
    padding: 4rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
    text-align: right;
    /* ⚠️ النصوص جهة اليمين */
}

.section h2 {
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.container {
    width: 100%;
    margin-inline: auto;
    padding-inline: 1rem;
    max-width: 1200px;
}

@media (max-width: 992px) {
    .container {
        max-width: 90%;
    }
}

@media (max-width: 576px) {
    .container {
        max-width: 100%;
        padding-inline: 0.5rem;
    }
}




/* ================================
     (Buttons - Forms)
================================== */
button,
.btn {
    background-color: var(--accent-color);
    color: #fff;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: var(--main-color);
}

input,
textarea,
select {
    width: 100%;
    padding: 0.75rem;
    margin-bottom: 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #fff;
    color: #000;
    text-align: right;
    /* ⚠️ حتى النص داخل الحقول */
}

textarea {
    height: 300px;
}

/* ================================
   استجابة الشاشات الصغيرة
================================== */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
    }

    .section {
        padding: 2rem 1rem;
    }
}


.main-header {
    background-color: var(--main-color);
    color: var(--text-color);
    padding: 1rem 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--secondary-color);
}

.main-nav ul {
    list-style: none;
    display: flex;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.main-nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.main-nav a:hover {
    color: var(--accent-color);
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 2rem;
    color: var(--text-color);
    cursor: pointer;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .menu-toggle {
        display: block;
    }

    .main-nav {
        display: none;
        position: absolute;
        top: 80px;
        right: 0;
        width: 100%;
        background-color: var(--main-color);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
                  z-index: 3;
    }

    .main-nav ul {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
        text-align: right;
    }

    .main-nav.active {
        display: block;
    }
}


/* ================================
    Start Profile
================================== */

.profile-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    /* لجعلها دائرية */
    object-fit: cover;
    margin-inline-start: 1rem;
    /* مسافة بين الصورة واللوجو (بالعربي نستخدم start بدل left) */
    border: 2px solid #fff;
    /* إطار أبيض بسيط */
    cursor: pointer;
    transition: transform 0.3s ease;
}

.profile-photo:hover {
    transform: scale(1.1);
}

/* في الجوال نجعل الصورة أصغر */
@media (max-width: 768px) {
    .profile-photo {
        width: 32px;
        height: 32px;
        margin-inline-start: 0.5rem;
        display: none;
    }

    .portfolio-link {
        display: block;
    }
}


h1 {
    font-weight: 700;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #1e88e5;
    text-align: center;
}

p.intro {
    font-weight: 400;
    font-size: 1.2rem;
    max-width: 700px;
    margin-inline: auto;
    margin-bottom: 2rem;
    text-align: center;
    color: #555;
}

ul.features {
    list-style: none;
    padding: 0;
    max-width: 700px;
    margin-inline: auto;
    margin-bottom: 3rem;
}

ul.features li {
    background-color: #e3f2fd;
    border-radius: 8px;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    font-weight: 500;
    color: #1565c0;
    box-shadow: 0 2px 6px rgba(21, 101, 192, 0.15);
    transition: background-color 0.3s ease;
}

ul.features li:hover {
    background-color: #bbdefb;
}

.cta-button {
    display: block;
    width: 200px;
    margin-inline: auto;
    padding: 0.75rem 0;
    text-align: center;
    background-color: #1e88e5;
    color: white;
    text-decoration: none;
    font-weight: 600;
    border-radius: 30px;
    box-shadow: 0 4px 8px rgba(30, 136, 229, 0.4);
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.cta-button:hover {
    background-color: #1565c0;
    box-shadow: 0 6px 12px rgba(21, 101, 192, 0.6);
}

/* Responsive */
@media (max-width: 600px) {
    h1 {
        font-size: 2rem;
    }

    p.intro,
    ul.features {
        max-width: 100%;
        padding-inline: 1rem;
    }
}


/* Login Form Styles */
.login-form {
    max-width: 450px;
    margin: 3rem auto;
    background-color: #fff;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    color: #333;
}

.login-form h2 {
    color: var(--main-color);
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.8rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #444;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.forgot-password {
    color: var(--main-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.forgot-password:hover {
    text-decoration: underline;
}

.login-button {
    width: 100%;
    padding: 0.75rem;
    background-color: var(--main-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
    font-family: 'Cairo', sans-serif;
}

.login-button:hover {
    background-color: #1565c0;
}

.register-link {
    text-align: center;
    margin-top: 1.5rem;
    color: #555;
}

.register-link a {
    color: var(--main-color);
    text-decoration: none;
    font-weight: 600;
}

.register-link a:hover {
    text-decoration: underline;
}

/* Registration Form Styles */
.register-form {
    max-width: 500px;
    /* Slightly wider than login form */
}

.terms-agree {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.terms-agree a {
    color: var(--main-color);
    text-decoration: none;
}

.terms-agree a:hover {
    text-decoration: underline;
}

/* Fix checkbox alignment in RTL */
input[type="checkbox"] {
    width: auto;
    margin-left: 0.5rem;
}

/* Email Verification Styles */
.verification-container {
    max-width: 600px;
    margin: 3rem auto;
    background-color: #fff;
    border-radius: 10px;
    padding: 2.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    color: #333;
    text-align: center;
}

.verification-icon {
    font-size: 4rem;
    color: #4CAF50;
    margin-bottom: 1rem;
    background-color: rgba(76, 175, 80, 0.1);
    width: 100px;
    height: 100px;
    line-height: 100px;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
}

.verification-container h2 {
    color: var(--main-color);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.verification-container p {
    margin-bottom: 1.5rem;
    color: #555;
    line-height: 1.6;
}

.verification-info {
    background-color: #f5f5f5;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.resend-button {
    background-color: var(--main-color);
    color: white;
    border: none;
    padding: 0.6rem 1.2rem;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
    font-family: 'Cairo', sans-serif;
    margin: 0.5rem 0;
}

.resend-button:hover {
    background-color: #1565c0;
}

.resend-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.countdown-timer {
    font-size: 0.9rem;
    color: #777;
    margin-top: 0.5rem;
}

.back-to-login a {
    color: var(--main-color);
    text-decoration: none;
    font-weight: 600;
}

.back-to-login a:hover {
    text-decoration: underline;
}

/* Verification Code Input Styles */
.verification-code-container {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 2rem 0;
}

.code-input {
    width: 50px;
    height: 60px;
    font-size: 1.5rem;
    text-align: center;
    border: 2px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.code-input:focus {
    border-color: var(--main-color);
    box-shadow: 0 0 0 2px rgba(30, 144, 255, 0.2);
    outline: none;
}

.verify-button {
    background-color: var(--main-color);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    margin-bottom: 2rem;
}

.verify-button:hover {
    background-color: #1565c0;
}

/* Forgot Password & Reset Password Styles */
.forgot-password-form .form-description,
.reset-password-form .form-description {
    margin-bottom: 1.5rem;
    color: #555;
    text-align: center;
}

.email-display {
    font-weight: 600;
    color: var(--main-color);
    margin-bottom: 1.5rem;
}

.password-strength {
    margin-top: 0.5rem;
    font-size: 0.85rem;
}

.password-strength.very-weak {
    color: #d32f2f;
}

.password-strength.weak {
    color: #f57c00;
}

.password-strength.medium {
    color: #fbc02d;
}

.password-strength.strong {
    color: #388e3c;
}

.password-strength.very-strong {
    color: #1b5e20;
}

/* Email Verification Success Page Styles */
.verification-success {
    max-width: 600px;
    margin: 3rem auto;
    padding: 2rem;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #4CAF50;
}

.verification-success h2 {
    color: #333;
    margin-bottom: 1rem;
}

.success-message {
    color: #555;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.next-steps {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.next-steps h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.next-step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.step-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.primary-button {
    background-color: var(--main-color);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    text-decoration: none;
    display: inline-block;
    margin-top: 1rem;
}

.primary-button:hover {
    background-color: #1565c0;
}

.secondary-button {
    background-color: transparent;
    color: var(--main-color);
    border: 1px solid var(--main-color);
    padding: 0.75rem 2rem;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    text-decoration: none;
    display: inline-block;
}

.secondary-button:hover {
    background-color: #f0f7ff;
}

.other-options {
    margin-top: 2rem;
}

h2.section-title {
    font-size: 2rem;
    color: #1e88e5;
    margin-bottom: 1rem;
    border-right: 4px solid #1e88e5;
    padding-right: 10px;
}

/* مشاريع */
.projects {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.project-card {
    background-color: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.project-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.project-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #1565c0;
}

.project-desc {
    font-size: 0.95rem;
    color: #555;
    margin-bottom: 0.8rem;
}

.project-meta {
    font-size: 0.85rem;
    color: #888;
}

/* مستقلين */
.freelancers {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.freelancer-card {
    background-color: white;
    border-radius: 10px;
    text-align: center;
    padding: 1rem;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
    position: relative;
}

.freelancer-card:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.freelancer-photo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 0.5rem;
    border: 2px solid #1e88e5;
}

.freelancer-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.3rem;
}

.verified-badge {
    background-color: #4caf50;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 5px;
    margin-right: 5px;
}

.freelancer-rating {
    color: #fbc02d;
    margin-bottom: 0.3rem;
}

.freelancer-job {
    font-size: 0.9rem;
    color: #777;
}

/* آراء العملاء */
.testimonials {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 3rem;
}

.testimonial-card {
    background: #fff;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-size: 0.95rem;
    color: #555;
    position: relative;
}

.testimonial-card::before {
    content: "“";
    font-size: 3rem;
    color: #1e88e5;
    position: absolute;
    top: 5px;
    right: 10px;
}

/* FAQ */
.faq {
    margin-bottom: 3rem;
}

.faq-item {
    background-color: white;
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.faq-question {
    font-weight: 600;
    color: #1565c0;
    margin-bottom: 0.5rem;
}

.faq-answer {
    font-size: 0.95rem;
    color: #555;
}

@media (max-width: 768px) {
    .projects {
        grid-template-columns: 1fr;
    }
}

/* Profile Photo Upload Page Styles */
.profile-upload {
    max-width: 800px;
    margin: 3rem auto;
    padding: 2rem;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.profile-upload h2 {
    text-align: center;
    margin-bottom: 1rem;
    color: #333;
}

.upload-description {
    text-align: center;
    color: #555;
    margin-bottom: 2rem;
}

.upload-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin-bottom: 2rem;
}

.upload-preview {
    flex: 1;
    min-width: 250px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.avatar-placeholder {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px dashed #ccc;
}

.avatar-icon {
    font-size: 5rem;
    color: #aaa;
}

.preview-image {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    display: none;
    border: 2px solid var(--main-color);
}

.upload-controls {
    flex: 1;
    min-width: 250px;
}

.upload-button {
    display: inline-block;
    background-color: var(--main-color);
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    margin-bottom: 1rem;
}

.upload-button:hover {
    background-color: #1565c0;
}

.upload-hint {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

.upload-requirements {
    background-color: #f9f9f9;
    padding: 1rem;
    border-radius: 5px;
}

.upload-requirements p {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.upload-requirements ul {
    padding-right: 1.5rem;
    color: #555;
}

.upload-requirements li {
    margin-bottom: 0.5rem;
}

.upload-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.primary-button:disabled {
    background-color: #b0b0b0;
    cursor: not-allowed;
}

@media (max-width: 768px) {
    .upload-container {
        flex-direction: column;
        align-items: center;
    }

    .upload-controls {
        width: 100%;
        text-align: center;
    }

    .upload-requirements {
        text-align: right;
    }
}

/* Account Type Selection Page Styles */
.account-type-container {
    max-width: 900px;
    margin: 3rem auto;
    padding: 2rem;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.account-type-header {
    text-align: center;
    margin-bottom: 3rem;
}

.profile-setup-header h2,
.account-type-header h2 {
    margin-bottom: 0.5rem;
    color: #333;
}

.step-description {
    color: #666;
    margin-bottom: 1rem;
}

/* Step indicator styles */
.step-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
}

.step {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    color: #666;
    position: relative;
}

.step.completed {
    background-color: #4CAF50;
    color: white;
}

.step.active {
    background-color: var(--main-color);
    color: white;
}

.step-line {
    flex-grow: 1;
    height: 3px;
    background-color: #f0f0f0;
    max-width: 100px;
}

/* Account type cards */
.account-types {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.account-type-card {
    flex: 1;
    padding: 2rem;
    border-radius: 10px;
    border: 2px solid #e0e0e0;
    background-color: #f9f9f9;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.account-type-card:hover {
    border-color: var(--main-color);
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.account-type-card.active {
    border-color: var(--main-color);
    background-color: #f0f7ff;
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.account-type-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.account-type-card h3 {
    color: #333;
    margin-bottom: 0.5rem;
}

.account-type-card p {
    color: #666;
    margin-bottom: 1.5rem;
}

.account-benefits {
    text-align: right;
    padding-right: 1.5rem;
    margin-bottom: 2rem;
}

.account-benefits li {
    margin-bottom: 0.75rem;
    color: #555;
}

.account-type-select {
    background-color: var(--main-color);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
}

.account-type-select:hover {
    background-color: #1565c0;
}

.account-type-footer {
    text-align: center;
}

.account-type-note {
    color: #777;
    font-size: 0.9rem;
}

/* Responsive styles */
@media (max-width: 768px) {
    .account-types {
        flex-direction: column;
    }

    .account-type-card {
        margin-bottom: 1.5rem;
    }
}

/* Skills input styles */
.skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.skill-tag {
    background-color: #f0f7ff;
    color: var(--main-color);
    border: 1px solid var(--main-color);
    border-radius: 20px;
    padding: 0.3rem 0.8rem;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
}

.remove-skill {
    margin-right: 0.5rem;
    cursor: pointer;
    font-weight: bold;
    font-size: 1.2rem;
}

.remove-skill:hover {
    color: #d32f2f;
}

@media (max-width: 768px) {
    .form-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .form-actions button {
        width: 100%;
    }
}

/* Identity Verification Page Styles */
.verification-container {
    max-width: 900px;
    margin: 3rem auto;
    padding: 2rem;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.verification-header {
    text-align: center;
    margin-bottom: 3rem;
}

.verification-header h2 {
    margin-bottom: 0.5rem;
    color: #333;
}

.verification-description {
    color: #666;
    margin-bottom: 1.5rem;
}

.verification-badge {
    background-color: #f0f7ff;
    border: 1px solid #d0e3ff;
    border-radius: 8px;
    padding: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 auto;
}

.badge-icon {
    background-color: var(--main-color);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
}

.verification-badge p {
    color: #555;
    margin: 0;
}

.verification-steps {
    margin-bottom: 2rem;
}

.verification-step {
    margin-bottom: 2.5rem;
    position: relative;
}

.step-number {
    background-color: var(--main-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    position: absolute;
    top: 0;
    right: -45px;
}

.verification-step h3 {
    margin-bottom: 1.5rem;
    color: #333;
    padding-right: 1.5rem;
}

.document-types {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.document-type-option {
    flex: 1;
    min-width: 150px;
}

.document-type-option input[type="radio"] {
    display: none;
}

.document-type-option label {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.document-type-option input[type="radio"]:checked+label {
    border-color: var(--main-color);
    background-color: #f0f7ff;
}

.document-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.document-upload {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.upload-side {
    flex: 1;
    min-width: 250px;
}

.upload-side h4 {
    margin-bottom: 1rem;
    color: #444;
}

.upload-area {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    position: relative;
    height: 200px;
    margin-bottom: 1rem;
    transition: border-color 0.3s;
}

.upload-area:hover {
    border-color: var(--main-color);
}

.upload-area.highlight {
    border-color: var(--main-color);
    background-color: #f0f7ff;
}

.upload-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.upload-icon {
    font-size: 3rem;
    color: #aaa;
    margin-bottom: 1rem;
}

.upload-placeholder p {
    color: #777;
}

.upload-preview {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    display: none;
}

.upload-button {
    background-color: var(--main-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    width: 100%;
}

.upload-button:hover {
    background-color: #1565c0;
}

.upload-requirements {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 1.5rem;
}

.upload-requirements h4 {
    margin-bottom: 1rem;
    color: #444;
}

.upload-requirements ul {
    padding-right: 1.5rem;
}

.upload-requirements li {
    margin-bottom: 0.5rem;
    color: #555;
}

.verification-consent {
    margin-top: 1rem;
}

.consent-checkbox {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
}

.consent-checkbox input {
    margin-top: 0.25rem;
}

.consent-checkbox span {
    color: #555;
}

.consent-checkbox a {
    color: var(--main-color);
    text-decoration: none;
}

.consent-checkbox a:hover {
    text-decoration: underline;
}

.verification-actions {
    text-align: center;
}

.verification-note {
    color: #777;
    font-size: 0.9rem;
    margin-top: 1rem;
}

.primary-button:disabled {
    background-color: #b0b0b0;
    cursor: not-allowed;
}

.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@media (max-width: 768px) {
    .verification-step {
        padding-right: 2.5rem;
    }

    .step-number {
        right: -10px;
    }

    .document-upload {
        flex-direction: column;
    }

    .verification-actions {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .verification-actions button,
    .verification-actions a {
        width: 100%;
    }
}

/* Registration Complete Page Styles */
.registration-complete {
    max-width: 900px;
    margin: 3rem auto;
    padding: 2rem;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: var(--main-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}

.registration-complete h2 {
    margin-bottom: 1rem;
    color: #333;
}

.success-message {
    font-size: 1.2rem;
    color: #555;
    margin-bottom: 2.5rem;
}

.account-summary {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2.5rem;
    text-align: right;
}

.account-summary h3 {
    margin-bottom: 1.5rem;
    color: #333;
    text-align: center;
}

.summary-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-icon {
    font-size: 2rem;
    margin-left: 1rem;
    width: 40px;
    text-align: center;
}

.summary-details {
    flex: 1;
}

.summary-details h4 {
    margin-bottom: 0.25rem;
    color: #444;
}

.summary-details p {
    color: #666;
    margin: 0;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.status-badge.completed {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.status-badge.pending {
    background-color: #fff8e1;
    color: #f57f17;
}

.status-badge.incomplete {
    background-color: #ffebee;
    color: #c62828;
}

.next-steps {
    margin-bottom: 2.5rem;
}

.next-steps h3 {
    margin-bottom: 1.5rem;
    color: #333;
}

.next-steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.next-step-card {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
}

.next-step-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.next-step-card .step-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.next-step-card h4 {
    margin-bottom: 0.75rem;
    color: #333;
}

.next-step-card p {
    color: #666;
    margin-bottom: 1.5rem;
    min-height: 3em;
}

.step-button {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: var(--main-color);
    color: white;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    transition: background-color 0.3s;
}

.step-button:hover {
    background-color: #1565c0;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.action-buttons a {
    min-width: 200px;
}

/* Confetti Animation */
.confetti-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
    overflow: hidden;
}

.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    top: -10px;
    border-radius: 0;
    animation: confetti-fall linear forwards;
}

@keyframes confetti-fall {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
    }

    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}

@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
    }

    .action-buttons a {
        width: 100%;
    }

    .summary-item {
        flex-direction: column;
        text-align: center;
    }

    .summary-icon {
        margin: 0 0 1rem 0;
    }

    .status-badge {
        margin-top: 1rem;
    }
}

/* Profile Page Styles */
.profile-container {
    max-width: 1100px;
    margin: 2rem auto;
}

.profile-header {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.profile-cover {
    height: 200px;
    background-color: #e0e0e0;
    background-image: linear-gradient(45deg, #2196f3, #3f51b5);
    background-size: cover;
    background-position: center;
    position: relative;
}

.edit-cover {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
}

.edit-button {
    background-color: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    transition: background-color 0.3s;
}

.edit-button:hover {
    background-color: white;
}

.edit-icon {
    font-size: 1.2rem;
}

.profile-info {
    padding: 0 2rem 2rem;
    position: relative;
}

.profile-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 5px solid white;
    background-color: #f0f0f0;
    position: absolute;
    top: -75px;
    right: 2rem;
    overflow: hidden;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.change-avatar {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: var(--main-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s;
}

.change-avatar:hover {
    background-color: #1565c0;
}

.profile-details {
    padding-top: 5rem;
    margin-bottom: 1.5rem;
}

.profile-name {
    margin-bottom: 0.5rem;
    color: #333;
}

.profile-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.profile-type {
    background-color: #e3f2fd;
    color: #1565c0;
    padding: 0.25rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.verification-status {
    background-color: #e3f2fd;
    color: #1565c0;
    padding: 0.25rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

/* Simple Profile Page Styles */
.simple-profile {
    max-width: 800px;
    margin: 2rem auto;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 2rem;
}

.profile-header-simple {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    gap: 2rem;
}

.profile-photo-container {
    text-align: center;
}

#profile-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--main-color);
    margin-bottom: 0.5rem;
            z-index: 2;
}

.change-photo-btn {
    background-color: transparent;
    border: 1px solid var(--main-color);
    color: var(--main-color);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s;
}

.change-photo-btn:hover {
    background-color: var(--main-color);
    color: white;
}

.profile-basic-info {
    flex: 1;
}

.profile-basic-info h2 {
    margin-bottom: 0.5rem;
    color: #333;
}

.user-type {
    display: inline-block;
    background-color: #e3f2fd;
    color: var(--main-color);
    padding: 0.25rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.verification-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    background-color: #fff8e1;
    color: #f57f17;
}

.verification-badge .verification-icon {
    font-size: 1.1rem;
}

.verification-badge.verified {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.verification-badge.not-verified {
    background-color: #ffebee;
    color: #c62828;
}

.profile-completion-bar {
    margin-bottom: 2rem;
}

.completion-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    color: #555;
    font-size: 0.9rem;
}

.progress-bar {
    height: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background-color: var(--main-color);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.profile-sections {
    margin-bottom: 2rem;
}

.profile-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #eee;
}

.profile-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.profile-section h3 {
    margin-bottom: 1rem;
    color: #333;
    position: relative;
    padding-right: 1rem;
}

.profile-section h3::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background-color: var(--main-color);
    border-radius: 2px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

.info-item {
    background-color: #f9f9f9;
    padding: 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.info-item-edit {
    background-color: #f9f9f9;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 10px;
}

.info-item .stutes {
    color: white;
    padding: 5px;
    font-size: 14px;
    border-radius: 6px;
    /* width: 40px; */
    display: block;
    text-align: center;
}

.info-item .stutes .complet {
    padding: 5px;
    background-color: var(--grenn-color);

}

.info-label {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.info-value {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    word-break: break-word;
}

.info-status {
    display: inline-block;
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
}

.info-status.verified {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.info-status.not-verified {
    background-color: #ffebee;
    color: #c62828;
}

.user-bio {
    line-height: 1.8;
    color: #333;
}

.verification-steps {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.verification-step {
    display: flex;
    align-items: center;
    background-color: #f9f9f9;
    padding: 1rem;
    border-radius: 8px;
    position: relative;
}

.step-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 1rem;
    font-size: 1rem;
    font-weight: bold;
}

.verification-step.completed .step-icon {
    background-color: #4caf50;
    color: white;
}

.verification-step.pending .step-icon {
    background-color: #ff9800;
    color: white;
}

.verification-step.not-started .step-icon {
    background-color: #e0e0e0;
    color: #757575;
}

.step-info {
    flex: 1;
}

.step-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.step-status {
    font-size: 0.9rem;
    color: #666;
}

.verify-now-btn {
    background-color: var(--main-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-family: 'Cairo', sans-serif;
}

.verify-now-btn:hover {
    background-color: #1565c0;
}

.profile-actions {
    display: flex;
    gap: 1rem;
}

.edit-profile-btn,
.dashboard-btn {
    flex: 1;
    display: block;
    text-align: center;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s;
}

.edit-profile-btn {
    background-color: var(--main-color);
    color: white;
}

.edit-profile-btn:hover {
    background-color: #1565c0;
}

.dashboard-btn {
    background-color: transparent;
    border: 1px solid var(--main-color);
    color: var(--main-color);
}

.dashboard-btn:hover {
    background-color: #e3f2fd;
}

@media (max-width: 768px) {
    .profile-header-simple {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .profile-actions {
        flex-direction: column;
    }
}

/* Profile Overview Styles */
.profile-overview {
    max-width: 800px;
    margin: 2rem auto;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    text-align: center;

}



.profile-icon {
    position: absolute;
    z-index: 1;
    font-size: 23px;
    color: var(--main-color);
    cursor: pointer;
}

.img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #1877F2;
    margin-bottom: 1rem;
    position: relative;
}

.image-upload-container {
    border: 2px dashed #ddd;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.image-upload-container:hover {
    border-color: var(--accent-color);
    background-color: rgba(72, 149, 239, 0.05);
}

.upload-icon {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.image-preview {
    max-width: 100%;
    max-height: 300px;
    border-radius: 10px;
    display: none;
    margin: 0 auto 1.5rem;
}

.btn-submit {
    background-color: var(--success-color);
    color: white;
    border: none;
    padding: 0.8rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(76, 201, 240, 0.3);
    width: 100%;
    margin-top: 1rem;
}

.btn-submit:hover {
    background-color: #3ab7d8;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(76, 201, 240, 0.4);
}

.btn-cancel {
    background-color: #f1f1f1;
    color: #666;
    border: none;
    padding: 0.8rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 1rem;
}

.profile-image-container {
    margin-bottom: 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

#profile-image {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #1877F2;
    margin-bottom: 1rem;
    position: relative;
}

.profile-image-container {}

.profile-image-container .profile-activity {}

.profile-image-container .profile-activity img {
    width: 30px;
    height: 30px;
    position: absolute;
    z-index: 1;
    margin-left: 10px;
}

.change-photo-btn {
    background-color: transparent;
    border: 1px solid #1877F2;
    color: #1877F2;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s;
    font-family: 'Cairo', sans-serif;
}

.change-photo-btn:hover {
    background-color: #1877F2;
    color: white;
}

.profile-name-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-bottom: 2rem;
}

.profile-name-container h2 {
    margin: 0;
    font-size: 1.8rem;
    color: #333;
}

.verified-badge {
    margin-right: 0.5rem;
    display: flex;
    align-items: center;
}

.verified-badge svg {
    width: 20px;
    height: 20px;
}

.profile-tabs {
    display: flex;
    justify-content: center;
    border-bottom: 1px solid #eee;
    margin-bottom: 2rem;
}

.tab-button {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    color: #666;
    cursor: pointer;
    position: relative;
    transition: color 0.3s;
}

.tab-button:hover {
    color: #1877F2;
}

.tab-button.active {
    color: #1877F2;
    font-weight: 600;
}

.tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #1877F2;
    border-radius: 3px 3px 0 0;
}

.tab-content {
    text-align: right;
}

.tab-content.hidden {
    display: none;
}

.profile-info-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #eee;
}

.profile-info-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.profile-info-section h3 {
    margin-bottom: 1rem;
    color: #333;
    position: relative;
    padding-right: 1rem;
}

.profile-info-section h3::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background-color: #1877F2;
    border-radius: 2px;
}

.info-item {
    margin-bottom: 1rem;
}

.info-label {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.info-value {
    font-weight: 600;
    color: #333;
}

.user-bio {
    line-height: 1.8;
    color: #333;
}

.empty-state {
    text-align: right;
    padding: 3rem 0;
    color: #666;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.profile-actions {
    margin-top: 2rem;
    display: flex;
    justify-content: center;
}

.edit-profile-btn {
    display: inline-block;
    background-color: #1877F2;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 600;
    transition: background-color 0.3s;
}

.edit-profile-btn:hover {
    background-color: #1565C0;
}

@media (max-width: 768px) {
    .profile-tabs {
        flex-wrap: wrap;
    }

    .tab-button {
        flex: 1 0 auto;
        padding: 0.75rem 1rem;
    }
}

/* Portfolio Styles */
.portfolio-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.add-portfolio-btn a {
    background-color: #1877F2;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    transition: background-color 0.3s;
}



.add-portfolio-btn:hover {
    background-color: #1565C0;
}

.add-portfolio-btn::before {
    content: '+';
    margin-left: 0.5rem;
    font-size: 1.2rem;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

.portfolio-item {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
    background-color: white;
}

.portfolio-item:hover {
    transform: translateY(-5px);
}

.portfolio-image {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.portfolio-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.1);
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-actions {
    display: flex;
    gap: 0.5rem;
}

.portfolio-actions a {
    background-color: #1877F2;
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-size: 0.8rem;
    transition: background-color 0.3s;
}

.view-portfolio-btn {
    background-color: #1877F2 !important;
}

.view-portfolio-btn:hover {
    background-color: #1565C0 !important;
}

.edit-portfolio-btn {
    background-color: #FF9800 !important;
}

.edit-portfolio-btn:hover {
    background-color: #F57C00 !important;
}

.delete-portfolio-btn {
    background-color: #F44336 !important;
}

.delete-portfolio-btn:hover {
    background-color: #D32F2F !important;
}

.portfolio-info {
    padding: 1rem;
    background-color: white;
}

.portfolio-info h4 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.portfolio-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Portfolio Modal */
.portfolio-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.8);
}

.portfolio-modal-content {
    background-color: white;
    margin: 5% auto;
    width: 80%;
    max-width: 900px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 20px;
    color: #333;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10;
}

.portfolio-modal-body {
    display: flex;
    flex-direction: column;
}

.portfolio-modal-image {
    width: 100%;
    height: 400px;
}

.portfolio-modal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.portfolio-modal-details {
    padding: 2rem;
    text-align: right;
}

.portfolio-modal-details h3 {
    margin-top: 0;
    color: #333;
    margin-bottom: 1rem;
}

.portfolio-modal-details p {
    color: #555;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.portfolio-meta {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    background-color: #f9f9f9;
    padding: 1rem;
    border-radius: 4px;
}

.meta-item {
    display: flex;
    flex-direction: column;
}

.meta-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

@media (min-width: 768px) {
    .portfolio-modal-body {
        flex-direction: row;
    }

    .portfolio-modal-image {
        width: 50%;
        height: auto;
    }

    .portfolio-modal-details {
        width: 50%;
    }
}

@media (max-width: 768px) {
    .portfolio-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .portfolio-modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .portfolio-modal-image {
        height: 250px;
    }
}


#preview-image {
    width: 200px;
    height: 200px;
    background-size: cover;
    background-position: center;
    background-color: #f0f0f0;
    border-radius: 50%;
    /* إذا أردت الشكل دائري */
    display: none;
    /* يظهر عند وجود صورة */
    margin: auto;
}


/* Profile Setup Styles */
.profile-setup-container {
    max-width: 900px;
    margin: 3rem auto;
    padding: 2.5rem;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
}

.profile-setup-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.step-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;
}

.step {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    position: relative;
}

.step.active {
    background-color: #1877F2;
    color: white;
    box-shadow: 0 0 0 5px rgba(24, 119, 242, 0.2);
}

.step.completed {
    background-color: #4CAF50;
    color: white;
}

.step-line {
    flex-grow: 1;
    height: 3px;
    background-color: #e9ecef;
    max-width: 80px;
}

.step-description {
    color: #6c757d;
    margin-top: 0.5rem;
    font-size: 1.1rem;
}

.profile-sections {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2.5rem;
}

.profile-section-card {
    display: flex;
    align-items: center;
    padding: 1.2rem;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.profile-section-card.active {
    border-color: #1877F2;
    background-color: #e3f2fd;
    box-shadow: 0 5px 15px rgba(24, 119, 242, 0.1);
}

.profile-section-card.completed {
    border-color: #4CAF50;
    background-color: #f1f8e9;
}

.section-icon {
    font-size: 1.8rem;
    margin-left: 1.2rem;
    width: 40px;
    text-align: center;
}

.section-content {
    flex-grow: 1;
}

.section-content h3 {
    margin: 0 0 0.3rem 0;
    font-size: 1.1rem;
    color: #333;
}

.section-content p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.section-status {
    font-size: 1.2rem;
    color: #6c757d;
    margin-right: 1rem;
}

.profile-section-card.completed .section-status {
    color: #4CAF50;
}

.profile-section-card.active .section-status {
    color: #1877F2;
}

/* Profile Upload Styles */
.profile-upload {
    margin-top: 2.5rem;
}







.upload-container {
    display: flex;
    flex-wrap: wrap;
    gap: 3rem;
    margin-bottom: 2.5rem;
    align-items: center;
}

.upload-preview-container {
    flex: 1;
    min-width: 280px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.2rem;
}

.upload-preview {
    width: 280px;
    height: 280px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border: 4px solid #f0f7ff;
    transition: all 0.3s ease;
}

.upload-preview:hover {
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-5px);
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f0f7ff;
}

.avatar-icon {
    font-size: 6rem;
    color: #90caf9;
}


.profile-icon {
    position: absolute;
    z-index: 1;
    font-size: 23px;
    color: var(--main-color);
    cursor: pointer;
}

.img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #1877F2;
    margin-bottom: 1rem;
    position: relative;
}

.image-upload-container {
    border: 2px dashed #ddd;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.image-upload-container:hover {
    border-color: var(--accent-color);
    background-color: rgba(72, 149, 239, 0.05);
}

.upload-icon {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.image-preview {
    max-width: 100%;
    max-height: 300px;
    border-radius: 10px;
    display: none;
    margin: 0 auto 1.5rem;
}

.btn-submit {
    background-color: var(--success-color);
    color: white;
    border: none;
    padding: 0.8rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(76, 201, 240, 0.3);
    width: 100%;
    margin-top: 1rem;
}

.btn-submit:hover {
    background-color: #3ab7d8;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(76, 201, 240, 0.4);
}

.btn-cancel {
    background-color: #f1f1f1;
    color: #666;
    border: none;
    padding: 0.8rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 1rem;
}


.preview-image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    display: none;
    transition: transform 0.3s ease;
}

.portfolio-info .control {
    margin-top: 20px;
    display: flex;
    /* width: 100px; */
    margin-bottom: 10px;
    gap: 20px;
}

.preview-controls {
    display: flex;
    gap: 0.8rem;
}

.control-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: none;
    background-color: #f8f9fa;
    color: #495057;
    font-size: 1.3rem;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.control-button:hover:not(:disabled) {
    background-color: #e9ecef;
    transform: translateY(-3px);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.15);
}

.control-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.upload-controls {
    flex: 1;
    min-width: 280px;
}

.upload-methods {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
    margin-bottom: 1.8rem;
}

.upload-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    border: 2px solid #e0e0e0;
    background-color: #f8f9fa;
    color: #495057;
    font-family: 'Cairo', sans-serif;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-button:hover {
    background-color: #e9ecef;
    border-color: #ced4da;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.primary-upload {
    background-color: #e3f2fd;
    border-color: #90caf9;
    color: #1565c0;
}

.primary-upload:hover {
    background-color: #bbdefb;
    border-color: #64b5f6;
}

.upload-icon {
    font-size: 1.6rem;
    margin-left: 1rem;
}

.upload-hint {
    margin: 1.2rem 0;
    color: #6c757d;
    font-size: 0.95rem;
    background-color: #f8f9fa;
    padding: 0.8rem;
    border-radius: 8px;
    border-right: 3px solid #90caf9;
}

.upload-requirements {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 1.2rem;
    margin-top: 1.8rem;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.upload-requirements h4 {
    margin-top: 0;
    color: #495057;
    font-size: 1.1rem;
    margin-bottom: 0.8rem;
}

.upload-requirements ul {
    margin: 0;
    padding-right: 1.5rem;
    color: #6c757d;
}

.upload-requirements li {
    margin-bottom: 0.7rem;
    font-size: 0.95rem;
}

.upload-progress {
    margin-bottom: 2rem;
    display: none;
}

.progress-bar {
    height: 10px;
    background-color: #e9ecef;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 0.8rem;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background-color: #1877F2;
    width: 0%;
    transition: width 0.3s ease;
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem;
    animation: progress-animation 1s linear infinite;
}

@keyframes progress-animation {
    from .tab-button.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 3px;
        background-color: #1877F2;
        border-radius: 3px 3px 0 0;
    }

    .tab-content {
        text-align: right;
    }

    .tab-content.hidden {
        display: none;
    }

    .profile-info-section {
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid #eee;
    }

    .profile-info-section:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }

    .profile-info-section h3 {
        margin-bottom: 1rem;
        color: #333;
        position: relative;
        padding-right: 1rem;
    }

    .profile-info-section h3::before {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background-color: #1877F2;
        border-radius: 2px;
    }

    .info-item {
        margin-bottom: 1rem;
    }

    .info-label {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .info-value {
        font-weight: 600;
        color: #333;
    }

    .user-bio {
        line-height: 1.8;
        color: #333;
    }

    .empty-state {
        text-align: center;
        padding: 3rem 0;
        color: #666;
    }

    .empty-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .profile-actions {
        margin-top: 2rem;
        display: flex;
        justify-content: center;
    }

    .edit-profile-btn {
        display: inline-block;
        background-color: #1877F2;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 4px;
        text-decoration: none;
        font-weight: 600;
        transition: background-color 0.3s;
    }

    .edit-profile-btn:hover {
        background-color: #1565C0;
    }

    @media (max-width: 768px) {
        .profile-tabs {
            flex-wrap: wrap;
        }

        .tab-button {
            flex: 1 0 auto;
            padding: 0.75rem 1rem;
        }
    }

    /* Portfolio Styles */
    .portfolio-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .add-portfolio-btn {
        background-color: #1877F2;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        font-family: 'Cairo', sans-serif;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        transition: background-color 0.3s;
    }

    .add-portfolio-btn:hover {
        background-color: #1565C0;
    }

    .add-portfolio-btn::before {
        content: '+';
        margin-left: 0.5rem;
        font-size: 1.2rem;
    }

    .portfolio-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    .portfolio-item {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
        background-color: white;
    }

    .portfolio-item:hover {
        transform: translateY(-5px);
    }

    .portfolio-image {
        position: relative;
        height: 180px;
        overflow: hidden;
    }

    .portfolio-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s;
    }

    .portfolio-item:hover .portfolio-image img {
        transform: scale(1.1);
    }

    .portfolio-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s;
    }

    .portfolio-item:hover .portfolio-overlay {
        opacity: 1;
    }

    .portfolio-actions {
        display: flex;
        gap: 0.5rem;
    }

    .portfolio-actions a {
        background-color: #1877F2;
        color: white;
        border: none;
        padding: 0.4rem 0.8rem;
        border-radius: 4px;
        cursor: pointer;
        font-family: 'Cairo', sans-serif;
        font-size: 0.8rem;
        transition: background-color 0.3s;
    }

    .view-portfolio-btn {
        background-color: #1877F2 !important;
    }

    .view-portfolio-btn:hover {
        background-color: #1565C0 !important;
    }

    .edit-portfolio-btn {
        background-color: #FF9800 !important;
    }

    .edit-portfolio-btn:hover {
        background-color: #e53935 !important;
    }

    background-color: #F57C00 !important;
}

.delete-portfolio-btn {
    background-color: #F44336 !important;
}

.delete-portfolio-btn:hover {
    background-color: #D32F2F !important;
}

.portfolio-info {
    padding: 1rem;
    background-color: white;
}

.portfolio-info h4 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.portfolio-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Portfolio Modal */
.portfolio-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.8);
}

.portfolio-modal-content {
    background-color: white;
    margin: 5% auto;
    width: 80%;
    max-width: 900px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 20px;
    color: #333;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10;
}

.portfolio-modal-body {
    display: flex;
    flex-direction: column;
}

.portfolio-modal-image {
    width: 100%;
    height: 400px;
}

.portfolio-modal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.portfolio-modal-details {
    padding: 2rem;
    text-align: right;
}

.portfolio-modal-details h3 {
    margin-top: 0;
    color: #333;
    margin-bottom: 1rem;
}

.portfolio-modal-details p {
    color: #555;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.portfolio-meta {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    background-color: #f9f9f9;
    padding: 1rem;
    border-radius: 4px;
}

.meta-item {
    display: flex;
    flex-direction: column;
}

.meta-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

@media (min-width: 768px) {
    .portfolio-modal-body {
        flex-direction: row;
    }

    .portfolio-modal-image {
        width: 50%;
        height: auto;
    }

    .portfolio-modal-details {
        width: 50%;
    }
}

@media (max-width: 768px) {
    .portfolio-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .portfolio-modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .portfolio-modal-image {
        height: 250px;
    }
}

/* Fix for hidden class */
.hidden {
    display: none !important;
}


/* project*/

.project-card {
    max-width: 750px;
    margin: 50px auto;
    border: none;
    box-shadow: 0 10px 30px rgba(67, 97, 238, 0.15);
    border-radius: 18px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.project-card:hover {
    transform: translateY(-5px);
}

.project-image {
    height: 400px;
    object-fit: cover;
    object-position: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.card-body {
    padding: 2rem;
}

.card-title {
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
}

.card-text {
    line-height: 1.8;
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 2rem;
}

.list-group-item {
    padding: 0.75rem 1.25rem;
    border-color: rgba(0, 0, 0, 0.05);
    font-size: 0.95rem;
}

.list-group-item strong {
    color: var(--primary-color);
    font-weight: 600;
}

.btn {
    font-weight: 500;
    padding: 0.6rem 1.2rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(67, 97, 238, 0.3);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #d91a6d;
    border-color: #d91a6d;
    transform: translateY(-2px);
}

.btn-warning {
    background-color: #ff9e00;
    border-color: #ff9e00;
    color: white;
}

.btn-warning:hover {
    background-color: #e68f00;
    border-color: #e68f00;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.action-buttons {
    margin-top: 2rem;
}

@media (max-width: 768px) {
    .project-card {
        margin: 20px auto;
    }

    .project-image {
        height: 250px;
    }

    .card-title {
        font-size: 1.5rem;
    }

    .card-text {
        font-size: 1rem;
    }

    .action-buttons .btn {
        margin-bottom: 10px;
        width: 100%;
    }
}

/* proposal */
.proposal-card,
.submitted-proposals {
    background: #fff;
    border-radius: 20px;
    padding: 30px;
    margin: 10px 0;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.6s ease-in-out;
    position: relative;
    overflow: hidden;
}

.proposal-card::before,
.submitted-proposals::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 8px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section-title {
    text-align: center;
    margin-bottom: 25px;
    color: var(--secondary-color);
    font-size: 24px;
    font-weight: 700;
    position: relative;
    padding-bottom: 10px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 50%;
    transform: translateX(50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 3px;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--dark-color);
    font-size: 15px;
}

input,
textarea {
    width: 100%;
    padding: 12px 15px;
    border-radius: 10px;
    border: 2px solid #3333330c;
    outline: none;
    font-size: 15px;
    font-family: 'Tajawal', sans-serif;
    transition: all 0.3s ease;
}

input:focus,
textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
    background-color: #fff;
}

textarea {
    resize: vertical;
    min-height: 120px;
}

/* تصميم تفاصيل الوظيفة */
.job-details-box {
    background: linear-gradient(135deg, #f0f4ff 0%, #e0e9ff 100%);
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #d0ddff;
}

.job-title {
    color: var(--accent-color);
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
    text-align: center;
}

.job-meta {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.job-meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: var(--text-muted);
}

.job-meta-item i {
    color: var(--primary-color);
}

.job-description {
    margin-top: 15px;
    font-size: 14px;
    line-height: 1.7;
}

.job-skills {
    margin-top: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.skill-tag {
    background-color: var(--primary-color);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
}

.price-container {
    display: flex;
    align-items: center;
}

.price-container span {
    background: linear-gradient(135deg, #f0f4ff 0%, #e0e9ff 100%);
    padding: 0 15px;
    height: 46px;
    display: flex;
    align-items: center;
    border: 2px solid #d0ddff;
    border-right: none;
    border-radius: 10px 0 0 10px;
    color: var(--secondary-color);
    font-weight: 600;
}

.price-container input {
    border-radius: 0 10px 10px 0;
    border-right: 2px solid var(--border-color);
}

.submit-btn {
    width: 100%;
    padding: 14px;
    border: none;
    background: linear-gradient(135deg, var(--main-color), var(--accent-color));
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
    box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
}

.submit-btn:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
}

.submit-btn:active {
    transform: translateY(0);
}

/* تصميم قسم العروض المقدمة */
.proposals-list {
    margin-top: 20px;
}

.proposal-item {
    background: #f9f9f9;
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.proposal-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.proposal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px dashed var(--border-color);
}

.proposal-price {
    background: linear-gradient(135deg, #e6f7ff 0%, #b3e0ff 100%);
    padding: 5px 12px;
    border-radius: 20px;
    color: var(--grenn-color);
    font-weight: 700;
    font-size: 14px;
}

.proposal-time {
    color: var(--text-muted);
    font-size: 13px;
}

.proposal-details {
    color: #444;
    font-size: 14px;
    line-height: 1.7;
}

.proposal-user {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
    font-size: 13px;
    color: var(--text-muted);
}

.user-avatar {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.no-proposals {
    text-align: center;
    padding: 30px;
    color: var(--text-muted);
    font-size: 16px;
}

@media (max-width: 768px) {

    .proposal-card,
    .submitted-proposals {
        padding: 20px;
    }

    .section-title {
        font-size: 22px;
    }

    .proposal-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

.empty {
    display: block;
    margin: auto;
    text-align: center;
    font-weight: bold;
    color: var(--orang-color);
}

/* *******************************proposal-card********************* */


/* *******************************proposal-card********************* */


/* **************************chat************************** */
        .chat-container {
            width: 100%;
            max-width: 400px;
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            box-shadow: var(--shadow);
            height: 600px;
        }

        .chat-header {
            background: var(--primary-color);
            color: var(--text-light);
            padding: 15px;
            font-size: 1.1rem;
            font-weight: bold;
            text-align: center;
            position: relative;
        }

        .chat-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            background: var(--background-light);
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .message {
            padding: 10px 15px;
            border-radius: var(--border-radius);
            max-width: 80%;
            word-wrap: break-word;
            position: relative;
            font-size: 0.95rem;
        }

        .message.sent {
            background: var(--message-sent);
            color: var(--text-light);
            align-self: flex-start;
            border-bottom-left-radius: 0;
        }

        .message.received {
            background: var(--message-received);
            color: var(--text-dark);
            align-self: flex-end;
            border-bottom-right-radius: 0;
        }

        .chat-input-container {
            display: flex;
            border-top: 1px solid #ddd;
            padding: 8px;
            background: white;
        }

        .chat-input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 20px;
            outline: none;
            font-size: 0.95rem;
            margin-right: 8px;
            transition: border 0.3s;
        }

        .chat-input:focus {
            border-color: var(--primary-color);
        }

        .send-button {
            padding: 10px 15px;
            border: none;
            background: var(--primary-color);
            color: white;
            cursor: pointer;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: var(--primary-hover);
        }

        /* Scrollbar styling */
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: #ccc;
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: #aaa;
        }

        /* Responsive adjustments */
        @media (max-width: 480px) {
            .chat-container {
                height: 100vh;
                border-radius: 0;
            }

            .message {
                max-width: 75%;
            }
        }
/* **************************chat************************** */


.job-title {
    font-size: 24px;
    font-weight: 700;
}

.job-status {
    background-color: white;
    color: var(--primary);
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
}

.content-wrapper {
    display: grid;
    grid-template-columns: 1fr;
    gap: 25px;
}

@media (min-width: 992px) {
    .content-wrapper {
        grid-template-columns: 1fr 1fr;
    }
}

.card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 25px;
    margin-top: 20px;

}

.card-title {
    font-size: 20px;
    color: var(--secondary);
    margin-bottom: 20px;
    padding-bottom: 10px;
    color: var(--main-color);
    border-bottom: 2px solid var(--primary-light);
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-title i {
    color: var(--primary);
}

/* تصميم قسم المستقل */
.freelancer-info {
    display: flex;
    gap: 40px;
    align-items: center;
    margin-bottom: 20px;
}

.freelancer-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 30px;
    font-weight: bold;
}

.freelancer-avatar img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}

.freelancer-details {
    flex: 1;
}

.freelancer-name {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 5px;
}

.freelancer-title {
    color: var(--text-light);
    margin-bottom: 10px;
}

.freelancer-rating {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--warning);
    color: var(--orang-color);
    margin-bottom: 10px;
}

.freelancer-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: var(--text-light);
}

.meta-item i {
    color: var(--accent-color);
}

/* تصميم قسم تفاصيل العرض */
.proposal-details {
    margin-top: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid var(--border);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    color: var(--text-light);
}

.detail-value {
    font-weight: 600;
}

.price {
    color: var(--grenn-color);
    font-size: 18px;
}

.proposal-description {
    background-color: var(--light);
    padding: 15px;
    border-radius: 8px;
    margin-top: 20px;
    line-height: 1.8;
}

/* تصميم قسم الوظيفة */
.job-details-list {
    list-style: none;
}

.job-details-list li {
    padding: 10px 0;
    border-bottom: 1px solid var(--border);
    display: flex;
    align-items: center;
    gap: 10px;
}

.job-details-list li:last-child {
    border-bottom: none;
}

.job-details-list i {
    color: var(--primary);
    width: 20px;
}

.skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.skill-tag {
    background-color: var(--primary-light);
    color: var(--primary);
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 14px;
}

/* تصميم قسم الإجراءات */
.actions-card {
    text-align: center;
}

.action-buttons {
    display: flex;
    gap: 15px;
    margin-top: 25px;
}

.btn {
    /* flex: 1; */
    padding: 15px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-accept {
    background: linear-gradient(135deg, var(--grenn-color), #2a9d8f);
    color: white;
}

.btn-accept:hover {
    background: linear-gradient(135deg, #2a9d8f, var(--main-color));
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(76, 201, 240, 0.4);
}

.btn-reject {
    background: linear-gradient(135deg, var(--danger-color), #e63946);
    color: white;
}

.btn-reject:hover {
    background: linear-gradient(135deg, #e63946, var(--danger-color));
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(247, 37, 133, 0.4);
}

.btn-message {
    background: linear-gradient(135deg, var(--main-color), var(--accent-color));
    color: white;
}

.btn-message:hover {
    background: linear-gradient(135deg, var(--main-color), var(--accent-color));
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.4);
}

.action-note {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    font-size: 14px;
    color: var(--text-light);
}

/* تصميم التوثيق والحماية */
.verification-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    background-color: var(--primary-light);
    color: var(--primary);
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    margin-top: 10px;
    background-color: #3488d9;
    color: white;
}

.verification-badge-an {
    color: #d63232ad;
    background-color: white;
}

.protection-notice {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-top: 20px;
    font-size: 14px;
}

.protection-notice i {
    color: var(--success);
    font-size: 20px;
}

/* *******************************proposal-card********************* */


/* *******************************settings********************* */
.settings-container {
    max-width: 500px;
    margin: 20px auto;
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.settings-container h2 {
    text-align: center;
    margin-bottom: 20px;
}

.settings-link {
    display: block;
    padding: 12px 20px;
    margin: 10px 0;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    transition: background-color 0.3s;
}

.settings-link:hover {
    background-color: #0056b3;
}

/* inbox */
    .inbox {
      max-width: 700px;
      margin: auto;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .chat-item {
      display: flex;
      align-items: center;
      padding: 15px;
      border-bottom: 1px solid #eee;
      transition: background 0.2s;
      cursor: pointer;
    }

    .chat-item:hover {
      background: #f0f2f5;
    }

    .avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      margin-left: 15px;
      object-fit: cover;
    }

    .chat-info {
      flex: 1;
    }

    .chat-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }

    .chat-name {
      font-weight: bold;
      font-size: 16px;
      color: #333;
    }

    .chat-time {
      font-size: 13px;
      color: #999;
    }

    .chat-message {
      font-size: 14px;
      color: #555;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 500px;
    }





/* Dashboard Styles */
.dashboard-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 2rem;
}

.dashboard-header h2 {
  color: var(--main-color);
  font-size: 2.2rem;
  margin-bottom: 0.5rem;
}

.dashboard-header p {
  color: #666;
  font-size: 1.1rem;
}

/* Statistics Section */
.stats-section {
  margin-bottom: 3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card.total {
  border-right: 4px solid #1877F2;
}

.stat-card.completed {
  border-right: 4px solid #28a745;
}

.stat-card.in-progress {
  border-right: 4px solid #ffc107;
}

.stat-card.open {
  border-right: 4px solid #17a2b8;
}

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.stat-info h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.3rem;
  color: #333;
}

.stat-info p {
  color: #666;
  font-size: 0.95rem;
  margin: 0;
}

/* Projects Section */
.projects-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tabs-container {
  width: 100%;
}

.tab-buttons {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.tab-button {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  font-family: 'Cairo', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.tab-button:hover {
  background: rgba(24, 119, 242, 0.1);
  color: var(--main-color);
}

.tab-button.active {
  background: white;
  color: var(--main-color);
  border-bottom: 3px solid var(--main-color);
}

.tab-count {
  background: var(--main-color);
  color: white;
  font-size: 0.8rem;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  min-width: 20px;
  text-align: center;
}

.tab-button.active .tab-count {
  background: var(--main-color);
}

.tab-content {
  display: none;
  padding: 2rem;
}

.tab-content.active {
  display: block;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Project Cards */
.project-card {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-right: 4px solid transparent;
}

.project-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.project-card.open-project {
  border-right-color: #17a2b8;
}

.project-card.progress-project {
  border-right-color: #ffc107;
}

.project-card.completed-project {
  border-right-color: #28a745;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.project-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.4;
}

.project-status {
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  white-space: nowrap;
}

.project-status.open {
  background: #d1ecf1;
  color: #0c5460;
}

.project-status.in-progress {
  background: #fff3cd;
  color: #856404;
}

.project-status.completed {
  background: #d4edda;
  color: #155724;
}

.project-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

.project-details {
  margin-bottom: 1.5rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.4rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #555;
  font-size: 0.9rem;
}

.detail-value {
  color: #333;
  font-size: 0.9rem;
}
















/* Fix for hidden class */
.hidden {
    display: none !important;
}


.footer {
    background-color: #0a0a23;
    color: #fff;
    padding: 50px 0 20px;
    direction: rtl;
    text-align: right;
}

.footer .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.footer-section h2,
.footer-section h3 {
    color: #fff;
    margin-bottom: 15px;
}

.footer-section p {
    color: #ccc;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section ul li a:hover {
    color: #ff9800;
}

.social-icons a {
    color: #ccc;
    margin-left: 10px;
    font-size: 20px;
    transition: color 0.3s;
}

.social-icons a:hover {
    color: #ff9800;
}

.footer-bottom {
    text-align: center;
    margin-top: 30px;
    border-top: 1px solid #333;
    padding-top: 15px;
    font-size: 14px;
    color: #aaa;
}

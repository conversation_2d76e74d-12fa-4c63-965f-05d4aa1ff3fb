@extends('front.layout.layout')

@section('body')
    <link rel="stylesheet" href="{{ asset('front/asset/css/dashboard.css') }}">


    @include('include.alert')

    <main class="container">
        <div class="dashboard-container">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <h2>لوحة إدارة المشاريع</h2>
                <p>إدارة ومتابعة جميع مشاريعك من مكان واحد</p>
            </div>

            <!-- Statistics Summary Section -->
            <div class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card total">
                        <div class="stat-icon">📊</div>
                        <div class="stat-info">
                            <h3 id="total-projects">{{ $status['all'] }}</h3>
                            <p>إجمالي المشاريع</p>
                        </div>
                    </div>

                    <div class="stat-card completed">
                        <div class="stat-icon">✅</div>
                        <div class="stat-info">
                            <h3 id="completed-projects">{{ $status['complat'] }}</h3>
                            <p>مشاريع مكتملة</p>
                        </div>
                    </div>

                    <div class="stat-card in-progress">
                        <div class="stat-icon">⚡</div>
                        <div class="stat-info">
                            <h3 id="progress-projects">{{ $status['close'] }}</h3>
                            <p>مشاريع قيد التنفيذ</p>
                        </div>
                    </div>

                    <div class="stat-card open">
                        <div class="stat-icon">📋</div>
                        <div class="stat-info">
                            <h3 id="open-projects">{{ $status['open'] }}</h3>
                            <p>مشاريع مفتوحة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project Tabs Section -->
            <div class="projects-section">
                <div class="tabs-container">
                    <div class="tab-buttons">
                        <button class="tab-button active" data-tab="open">
                            المشاريع المفتوحة <span class="tab-count" id="open-count">{{ $status['open'] }}</span>
                        </button>
                        <button class="tab-button" data-tab="progress">
                            قيد التنفيذ <span class="tab-count" id="progress-count">{{ $status['close'] }}</span>
                        </button>
                        <button class="tab-button" data-tab="completed">
                            المكتملة <span class="tab-count" id="completed-count">{{ $status['complat'] }}</span>
                        </button>
                    </div>

                    <!-- Open Projects Tab -->
                    <div class="tab-content active" id="open-tab">
                        <div class="projects-grid" id="open-projects-grid">
                            {{--  --}}
                            @foreach ($openWorks as $openWork)
                                <div class="project-card open-project">
                                    <div class="project-header">
                                        <h3 class="project-title">{{ $openWork->category_name }}</h3>
                                        <span class="project-status open">مفتوح</span>
                                    </div>
                                    <p class="project-description">{{ $openWork->name }}
                                    </p>
                                    <div class="project-details">
                                        <div class="detail-item">
                                            <span class="detail-label">الميزانية:</span>
                                            <span class="detail-value">{{ $openWork->category_name }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">المدة:</span>
                                            <span class="detail-value">{{ $openWork->expiration_date }} </span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">العروض:</span>
                                            <span class="detail-value">{{ $openWork->proposals_count }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">تاريخ النشر:</span>
                                            <span class="detail-value">
                                                {{ \Carbon\Carbon::parse($openWork->created_at)->format('Y-m-d') }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="project-actions">
                                        <form action="{{ route('user.work.display', $openWork->id) }}" method="get">

                                            <button class="primary-button">عرض العروض</button>
                                        </form>
                                        <form action="{{ route('user.work.edit', $openWork->id) }}" method="get">

                                            <button class="outline-button">تعديل</button>
                                        </form>
                                    </div>
                                </div>
                            @endforeach
                            {{--  --}}
                        </div>
                    </div>
                    {{-- 'u.name as ',
                 'u.id as user_id',

                 'w.',
                 'w.',
                 'w.',
                 'w.',
                 'p.proposal_id' --}}
                    <!-- In Progress Projects Tab -->
                    <div class="tab-content" id="progress-tab">
                        <div class="projects-grid" id="progress-projects-grid">
                            {{--  --}}
                            @foreach ($offers as $offer)
                                <div class="project-card progress-project">
                                    <div class="project-header">
                                        <h3 class="project-title"> {{ $offer->work_name }}</h3>
                                        <span class="project-status in-progress">قيد التنفيذ</span>
                                    </div>
                                    <p class="project-description" id="incres-text"> {{ $offer->description }}</p>
                                    <div class="project-details">
                                        <div class="detail-item">
                                            <span class="detail-label">الميزانية:</span>
                                            <span class="detail-value">{{ $offer->amount }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">المستقل:</span>
                                            <span class="detail-value"> {{ $offer->user_name }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">تاريخ البدء:</span>
                                            <span class="detail-value">
                                                {{ \Carbon\Carbon::parse($offer->created_at)->format('Y-m-d') }}
                                            </span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">عدد ايام التنفيذ :</span>
                                            <span class="detail-value">{{ $offer->expiration_date }}

                                            </span>
                                        </div>
                                    </div>
                                    {{-- <div class="progress-section">
                                    <div class="progress-info">
                                        <span>التقدم: 65%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 65%"></div>
                                    </div>
                                </div> --}}
                                    <div class="project-actions">
                                        <form action="{{ route('user.Proposal_decision.index', $offer->id) }}"
                                            method="GET">
                                            <button class="primary-button">عرض التفاصيل</button>
                                        </form>
                                        <form action="{{ route('user.message', $offer->user_id) }}" method="GET">
                                            <button class="secondary-button">التواصل</button>
                                        </form>
                                    </div>
                                </div>
                            @endforeach
                            {{--  --}}
                        </div>
                    </div>

                    {{-- 'u.name as ',
                'u.id as user_id',
                'w.name as work_name',
                'w.amount',
                'w.',
                'w.',
                'w.created_at',
                'p.proposal_id',
                'p.' --}}
                    <!-- Completed Projects Tab -->
                    <div class="tab-content" id="completed-tab">
                        <div class="projects-grid" id="completed-projects-grid">
                            @foreach ($complatWork as $complatWorks)
                                <div class="project-card completed-project">
                                    <div class="project-header">
                                        <h3 class="project-title"> {{ $complatWorks->work_name }}</h3>
                                        <span class="project-status completed">مكتمل</span>
                                    </div>
                                    <p class="project-description">{{ $complatWorks->description }}</p>
                                    <div class="project-details">
                                        <div class="detail-item">
                                            <span class="detail-label">الميزانية:</span>
                                            <span class="detail-value">{{ $complatWorks->amount }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">المستقل:</span>
                                            <span class="detail-value"> {{ $complatWorks->user_name }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">تاريخ الإكمال:</span>
                                            <span class="detail-value">
                                                {{ \Carbon\Carbon::parse($complatWorks->updated_at)->format('Y-m-d') }}

                                            </span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">المدة:</span>
                                            <span class="detail-value">{{ $complatWorks->expiration_date }}</span>
                                        </div>
                                    </div>
                                    <div class="rating-section">
                                        <span class="rating-label">التقييم:</span>
                                        <span class="rating-stars">
                                            @php
                                                $fullStars = floor($complatWorks->rating);
                                                $halfStar = $complatWorks->rating - $fullStars >= 0.5;

                                                $emptyStars = 5 - $fullStars - ($halfStar ? 1 : 0);

                                                for ($i = 0; $i < $fullStars; $i++) {
                                                    echo '⭐';
                                                }
                                                for ($i = 0; $i < $emptyStars; $i++) {
                                                    echo '★';
                                                }

                                            @endphp
                                        </span>
                                        <span class="rating-value">5/{{ $complatWorks->rating }}</span>
                                    </div>
                                    <div class="project-actions">
                                        {{-- <button class="outline-button" onclick="viewProjectDetails(8)">عرض التفاصيل</button> --}}
                                        <button class="secondary-button" onclick="downloadInvoice(8)">تحميل
                                            الفاتورة</button>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
@section('scriptFile')
    <script src="{{ asset('front/asset/js/user-dashboard.js') }}"></script>
    <script src="{{ asset('front/asset/js/menu-toggle.js') }}"></script>
    <script src="{{ asset('front/asset/js/text.js') }}"></script>
@endsection
@endsection



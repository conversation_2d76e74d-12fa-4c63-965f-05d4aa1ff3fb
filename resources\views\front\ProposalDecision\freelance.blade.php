@extends('front.layout.layout')

@section('body')

 @include('include.alert')

 <link rel="stylesheet" href="{{asset('front/asset/css/pages/job-acceptance.css')}}">

  <main class="container">
    <div class="job-acceptance-container">

      <!-- Header -->
      <div class="acceptance-header">
        <div class="success-icon"><i class="fas fa-check-circle"></i></div>
        <h2> ابشر تم قبول طلبك بنجاح!</h2>
        <p class="acceptance-message">باشر العمل و يمكنك التواصل مع صاحب العمل من اجل التفاصيل</p>
      </div>

      <!-- Project details -->
      <div class="card">
        <h3><i class="fas fa-tasks"></i> تفاصيل المشروع</h3>
        <div class="job-info">
          <div class="info-item"><span class="info-label"><i class="fas fa-heading"></i> عنوان المشروع:</span>  {{$data->work_name}}</div>
          <div class="info-item"><span class="info-label"><i class="fas fa-money-bill-wave"></i> الميزانية:</span>  {{$data->amount}}</div>
          <div class="info-item"><span class="info-label"><i class="fas fa-calendar-alt"></i> المدة المتوقعة:</span> {{$data->expiration_date}} </div>
          <div class="info-item"><span class="info-label"><i class="fas fa-play-circle"></i> تاريخ البدء:</span>
                         {{\Carbon\Carbon::parse($data->created_at)->format("Y-m-d")}}
             </div>
          <div class="info-item"><span class="info-label"><i class="fas fa-info-circle"></i> الحالة الحالية:</span> <span class="status-badge in-progress"><i class="fas fa-spinner"></i> قيد التنفيذ</span></div>
        </div>
      </div>

      <!-- Worker details -->
      <div class="card">
        <h3><i class="fas fa-user-tie"></i> معلومات صاحب العمل </h3>
        <div class="worker-profile">
          <div class="worker-avatar"><img src="{{asset($data->avater)}}" alt="صورة العامل"></div>
          <div class="worker-info">
            <h4>{{$data->name}}  </h4>
            {{-- <p class="worker-title"> {{$data->cname}}  </p> --}}
            <div class="worker-rating">
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star-half-alt"></i>
              4.8 (127 تقييم)
            </div>
            <div class="worker-contact"><i class="fas fa-envelope"></i> {{$data->email}} </div>
          </div>
        </div>
      </div>

      <!-- Management -->
      <div class="card">
        <h3><i class="fas fa-cogs"></i> إدارة المشروع</h3>
        <div class="management-actions">

          <form action="{{route('user.message',$data->user_id)}}" method="GET">
              <button class="secondary-button"><i class="fas fa-comments"></i> التواصل مع العامل</button>
          </form>
          {{-- <button class="outline-button"><i class="fas fa-file-download"></i> تحميل الملفات</button> --}}
        </div>
        <div class="progress-tracker">
          <div class="progress-step completed">
            <div class="step-circle"><i class="fas fa-check"></i></div>
            <span>تم قبول العرض</span>
          </div>
          <div class="progress-step active">
            <div class="step-circle">2</div>
            <span>العمل قيد التنفيذ</span>
          </div>
          <div class="progress-step">
            <div class="step-circle">3</div>
            <span>تم إنجاز العمل</span>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="additional-actions">
        {{-- <a href="{{route('user.userDashboard')}}" class="secondary-button"><i class="fas fa-tachometer-alt"></i> العودة إلى لوحة التحكم</a> --}}
        <a href="{{route('user.work.display',$data->work_id)}}" class="outline-button"><i class="fas fa-eye"></i> عرض تفاصيل المشروع</a>
        <a href="#" class="outline-button"><i class="fas fa-download"></i> تحميل العقد</a>
      </div>
    </div>
  </main>


</body>
</html>


    @section('scriptFile')
        <script src="{{asset('front/asset/js/menu-toggle.js')}}"></script>
    @endsection
@endsection

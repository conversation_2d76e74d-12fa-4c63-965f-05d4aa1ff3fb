<?php

namespace App\Http\Controllers\back;

use App\Http\Controllers\Controller;
use App\Http\Requests\back\LoginReques;
use App\Models\back\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;


class AdminController extends Controller
{
    public function index() {

        return view('back.index');
    }

    public function login(){

        return view('back.auth.login');
    }

    
    public function check_login(LoginReques $request){

        $admin = Admin::where('email',$request->email)->first();

        if(!$admin) {

            return Redirect()->back()->with('error','email or password was rong try agn');
        }


        if($request->password == $admin->password){

          Auth::guard('admins')->login($admin);

            return Redirect()->route('admin.home');

        }

        return Redirect()->back()->with('error','email or password was rong try agn');

    }

    public function logout(Request $request) {

          Auth::logout();

          $request->session()->invalidate();

          $request->session()->regenerateToken();

         return Redirect()->route('admin.login');
          
    }


}



@extends('front.layout.layout')

@section('body')

 @include('include.alert')
   <style>


@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

h2, h3 {
  margin-top: 0;
  color: var(--gray-900);
}

.success-icon {
  text-align: center;
  margin-bottom: 1rem;
  animation: bounce 1s;
}

.success-icon i {
  font-size: 4rem;
  color: var(--success);
  background: #d1fae5;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(16, 185, 129, 0.2);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {transform: translateY(0);}
  40% {transform: translateY(-20px);}
  60% {transform: translateY(-10px);}
}

.status-message {
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
  color: var(--success);
  font-weight: 700;
}

.status-description {
  text-align: center;
  color: var(--gray-700);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.rating-section {
  margin-top: 1.5rem;
  padding: 2rem;
  border: 1px solid var(--gray-300);
  border-radius: 12px;
  background: var(--gray-100);
  transition: all 0.3s ease;
}

.rating-section:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.star-rating {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin: 1.5rem 0;
}

.star-rating .star {
  font-size: 2.5rem;
  cursor: pointer;
  color: var(--gray-300);
  transition: all 0.2s ease;
}

.star-rating .star:hover,
.star-rating .star.active {
  color: var(--warning);
  transform: scale(1.2);
}

.rating-text {
  text-align: center;
  font-weight: 600;
  color: var(--gray-700);
  margin: 1rem 0 1.5rem;
  min-height: 1.5em;
  transition: color 0.3s ease;
}

.rating-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: var(--gray-700);
}

textarea {
  width: 100%;
  border: 1px solid var(--gray-400);
  border-radius: 10px;
  padding: 1rem;
  margin-top: 0.5rem;
  font-family: inherit;
  transition: border-color 0.3s;
  resize: vertical;
}

textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.rating-actions {
  margin-top: 1.5rem;
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.primary-button, .outline-button {
  padding: 0.8rem 1.8rem;
  border-radius: 10px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
  font-size: 1rem;
}

.primary-button {
  background: var(--primary);
  color: #fff;
}

.primary-button:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.outline-button {
  background: transparent;
  border: 1px solid var(--gray-400);
  color: var(--gray-700);
}

.outline-button:hover {
  background: var(--gray-200);
  transform: translateY(-2px);
}

.confirmation {
  text-align: center;
  padding: 2rem;
  display: none;
}

.confirmation i {
  font-size: 4rem;
  color: var(--success);
  margin-bottom: 1rem;
}

.confirmation h3 {
  color: var(--success);
  margin-bottom: 1rem;
}

@media (max-width: 600px) {
  .container {
    padding: 1.5rem;
    margin: 1rem auto;
  }

  .rating-section {
    padding: 1.5rem;
  }

  .star-rating .star {
    font-size: 2rem;
  }

  .rating-actions {
    flex-direction: column;
  }

  .primary-button, .outline-button {
    width: 100%;
    justify-content: center;
  }
}

  </style>

<div class="container">
    <!-- رسالة إتمام المشروع -->
    <div class="success-icon"><i class="fas fa-check-circle"></i></div>
    <div class="status-message">تم إنجاز المشروع بنجاح!</div>
    <p class="status-description">يرجى تقييم العامل المستقل لمساعدته على تحسين خدماته وتقديم تجربة أفضل للجميع.</p>

    <!-- قسم التقييم -->
    <div class="rating-section" id="rating-section">
        <h3>تقييم العامل المستقل</h3>

        <form id="rating-form" method="POST" action="{{ route('user.rate.create') }}">
            @csrf
            <input type="hidden" name="rating" id="rating-value">
            {{-- <input type="hidden" name="project_id" value="{{ $project->id ?? 1 }}">
            <input type="hidden" name="worker_id" value="{{ $worker->id ?? 1 }}"> --}}
            <input style="display: none" type="text" name="freelance_id" value="{{$ID['freelance_id']}}">
            <input style="display: none" type="text" name="work_id" value="{{$ID['work_id']}}">
            <input style="display: none" type="text" name="decision" value="{{$ID['decision']}}">
            <div class="star-rating" id="star-rating">
                <span class="star" data-value="1"><i class="far fa-star"></i></span>
                <span class="star" data-value="2"><i class="far fa-star"></i></span>
                <span class="star" data-value="3"><i class="far fa-star"></i></span>
                <span class="star" data-value="4"><i class="far fa-star"></i></span>
                <span class="star" data-value="5"><i class="far fa-star"></i></span>
            </div>

            <p class="rating-text" id="rating-text">اختر عدد النجوم للتقييم</p>

            <label for="worker-comment">اكتب تعليقك (اختياري):</label>
            <textarea name="comment" id="worker-comment" rows="4" placeholder="شارك تجربتك مع العامل المستقل..."></textarea>

            <div class="rating-actions">
                <button type="submit" class="primary-button">إرسال التقييم</button>
                <a href="{{ url()->previous() }}" class="outline-button">تخطي</a>
            </div>
        </form>
    </div>
</div>

<script>
    const stars = document.querySelectorAll('.star');
    const ratingInput = document.getElementById('rating-value');
    const ratingText = document.getElementById('rating-text');
    let currentRating = 0;

    stars.forEach(star => {
        star.addEventListener('click', () => {
            currentRating = star.getAttribute('data-value');
            ratingInput.value = currentRating; // حفظ القيمة في input مخفي
            updateStars(currentRating);
        });

        star.addEventListener('mouseover', () => {
            const hoverValue = star.getAttribute('data-value');
            updateStars(hoverValue);
        });

        star.addEventListener('mouseout', () => {
            updateStars(currentRating);
        });
    });

    function updateStars(rating) {
        stars.forEach(star => {
            const starValue = star.getAttribute('data-value');
            const starIcon = star.querySelector('i');

            starIcon.classList.remove('fas', 'far');

            if (starValue <= rating) {
                starIcon.classList.add('fas');
                star.classList.add('active');
            } else {
                starIcon.classList.add('far');
                star.classList.remove('active');
            }
        });

        const ratingLabels = {
            1: "ضعيف - لم يكن الأداء كما هو متوقع",
            2: "مقبول - هناك مجال للتحسين",
            3: "جيد - قام بالعمل المطلوب بشكل جيد",
            4: "جيد جداً - تجربة مرضية وتفوق التوقعات",
            5: "ممتاز - أداء استثنائي وجهد مميز"
        };

        ratingText.textContent = ratingLabels[rating] || "اختر عدد النجوم للتقييم";
        ratingText.style.color = rating ? '#f59e0b' : '#374151';
    }
</script>


    @section('scriptFile')
        <script src="{{asset('front/asset/js/menu-toggle.js')}}"></script>
    @endsection
@endsection

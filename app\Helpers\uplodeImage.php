<?php

namespace App\Helpers;

use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\File;

class uplodeImage
{
    public static function uplode($request ,$path = 'assets/imgs/default/', $update = '')
    {
    
        if (!empty($update)) {

            File::delete(public_path($update));
        }
        //uplode image 
        $file_Extension = $request->getClientOriginalExtension();

        $file_Name = time() . "." . $file_Extension;

        $last = $path . $file_Name;

        $request->move($path, $file_Name);

       return  $last ;
    }

}




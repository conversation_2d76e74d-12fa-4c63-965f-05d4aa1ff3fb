<?php

use App\Http\Controllers\back\AdminController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\front\AuthController;
use App\Http\Controllers\front\IndoxController;
use App\Http\Controllers\front\MessageController;
use App\Http\Controllers\front\ProfileController;
use App\Http\Controllers\front\ProjectController;
use App\Http\Controllers\front\ProposalController;
use App\Http\Controllers\front\SettingController;
use App\Http\Controllers\front\SubmittingController;
use App\Http\Controllers\front\WorkController;
use App\Http\Controllers\Proposal_decisionController;
use App\Http\Controllers\RateController;
use App\Http\Middleware\back\RedirectIfLogin;
use App\Models\front\Categorie;
use App\Models\front\Category;
use App\Models\front\Job_title;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;



session_start();

Route::get('/', [AuthController::class, 'index'])->name('index');

//Route::get('/posts', [AuthController::class, 'test']);


Route::get('/posts/{id}', [ProfileController::class, 'userProfileAPI']);



Route::get('/categorie', function () {
            $departments = Categorie::all(); // الأقسام
            return $departments;
});
Route::get('/get-jobs/{id}', function ($id) {
            $departments = Job_title::where('category_id' ,$id)->get(); // الأقسام
            return $departments;
});

Route::get('/Auth/job', function () {


    $job = Job_title::select('id','name')->where('id' ,Auth::user()->job_id)->first();
    // الأقسام
   $departments = Categorie::select('name','id')->where('id' ,Auth::user()->categorie_id)->first(); // الأقسام

            $all = [
                'user' =>Auth::user()->name,
                'job' =>$job ,
                'departments'=>$departments
            ];

            return $all ;
});


Route::get('/test', [AuthController::class, 'test']);

Route::post('/test/save', [AuthController::class, 'save'])->name('test');



    Route::get('forgot-password', [AuthController::class, 'forgot'])->name('user.forgot');
    Route::post('forgotPassword/send', [AuthController::class, 'forgot_send'])->name('user.forgot.send');
    Route::post('forgotPassword/code', [AuthController::class, 'forgot_code'])->name('user.forgot.code');
    Route::get('forgotPassword/trayAgen', [AuthController::class, 'forgot_tryagan'])->name('user.tryAgan_forget');
    Route::get('Password/store', [AuthController::class, 'chanc_password'])->name('user.chanc.password');
    Route::post('Password/set', [AuthController::class, 'save_password'])->name('user.save.password');


Route::group([ 'middleware' => 'guest'], function () {

    Route::get('/login', [AuthController::class, 'login'])->name('login');
    Route::get('/register', [AuthController::class, 'register'])->name('user.register');
    Route::post('/login', [AuthController::class, 'check_login'])->name('user.login.check');
    Route::post('check/register', [AuthController::class, 'check_register'])->name('user.register.check');

});

Route::group([ 'middleware' => 'auth'], function () {
    // logout
    Route::get('logout', [AuthController::class, 'logout'])->name('user.logout');
    //verification
    Route::get('verification', [AuthController::class, 'verification'])->name('user.verification');
    Route::post('verification', [AuthController::class, 'check_verification'])->name('user.check_verification');
    Route::get('verification-success', [AuthController::class, 'success_verification'])->name('user.success_verification');
    Route::get('verification-filled', [AuthController::class, 'tryAgan'])->name('user.tryAgan_verification');

    Route::group([ 'middleware' => 'verification'], function () {
        //uploade photo
        Route::get('photo', [AuthController::class, 'photo_index'])->name('user.photo_index');
        Route::post('/profile/setup/step3', [ProfileController::class, 'update_photo'])->name('profile.setup.step3');
        //select account type
        Route::get('account-type', [ProfileController::class, 'account_type'])->name('user.account-type');
        Route::post('account-type/update', [ProfileController::class, 'account_type_update'])->name('user.account-type.update');
        //complete freelancer setup
        Route::get('/get-jobs/{category_id}', function ($category_id) {
            return Job_title::where('category_id', $category_id)->get();
        });
        Route::get('freelancer/profile/setup', [ProfileController::class, 'freelancer_profile_setup'])->name('user.freelancer_profile_setup');
        Route::post('freelancer-setup-save', [ProfileController::class, 'freelancer_save_setup'])->name('user.freelancer_save_setup');
        //complete employer setup
        Route::get('employer/setup', [ProfileController::class, 'employer_setup'])->name('user.employer_profile_setup');
        Route::post('employer/setup', [ProfileController::class, 'employer_save_setup'])->name('user.employer_save_setup');
        //uploade identity
        Route::get('identity-verification', [ProfileController::class, 'identity'])->name('user.identity');
        Route::post('identity-verification', [ProfileController::class, 'identity_verification'])->name('user.identity.verification');
        //complete registration
        Route::get('registration-complete', [ProfileController::class, 'complete'])->name('user.registration.complete');
        //profile page
        Route::get('profile', [ProfileController::class, 'index'])->name('user.profile.index')->middleware('profile');
        Route::get('profile/user', [ProfileController::class, 'userProfile'])->name('user.profile.user.index')->middleware('UserProfile');
        //profile setting
         Route::get('profile/edit', [ProfileController::class, 'edit'])->name('user.profile.edit');
         Route::post('profile/update', [ProfileController::class, 'update'])->name('user.profile.update');
         Route::get('profile/edit/accont', [ProfileController::class, 'edit_accont'])->name('user.profile.edit_accont');
         Route::get('profile/Project/{id}', [ProfileController::class, 'show_Project'])->name('user.profile.show.Project');
         //Profile Visit
         Route::get('profile/Visit/user/{id}', [ProfileController::class, 'Visit_user'])->name('user.profile.Visit.user');

         //Projects Route
         Route::get('Projects/', [ProjectController::class, 'index'])->name('user.projects');
       //  Route::get('Project/{id}', [ProjectController::class, 'show'])->name('user.projects.show');
         Route::get('Project/create', [ProjectController::class, 'create'])->name('user.projects.create');
         Route::post('Project/save', [ProjectController::class, 'save'])->name('user.projects.save');
         Route::post('Project/delete/{id}', [ProjectController::class, 'delete'])->name('user.projects.delete');
         Route::get('projects/edit/{id}', [ProjectController::class, 'edit'])->name('user.projects.edit');
         Route::post('projects/update/{id}', [ProjectController::class, 'update'])->name('user.projects.update');
         Route::get('projects/porfile', [ProjectController::class, 'back'])->name('user.projects.redirect');

         //Works for users //

         Route::get('work/', [WorkController::class, 'index'])->name('user.work');
         Route::get('work/create', [WorkController::class, 'create'])->name('user.work.create');
         Route::post('work/save', [WorkController::class, 'save'])->name('user.work.save');
         Route::get('work/edit/{id}', [WorkController::class, 'edit'])->name('user.work.edit');
         Route::post('work/update/{id}', [WorkController::class, 'update'])->name('user.work.update');
         Route::get('work/display/{id}', [WorkController::class, 'display'])->name('user.work.display');
         Route::get('work/delete/{id}', [WorkController::class, 'delete'])->name('user.work.delete');


         //setting routes
         Route::get('setting/', [SettingController::class, 'index'])->name('user.setting');
         Route::get('setting/work', [SettingController::class, 'work'])->name('user.setting.work');
         Route::post('setting/work/update', [SettingController::class, 'workUpdate'])->name('user.setting.update');
         Route::get('setting/account', [SettingController::class, 'account_type'])->name('user.setting.account');
         Route::post('setting/account/update', [SettingController::class, 'accountType_update'])->name('user.setting.account.update');
         Route::get('setting/email', [SettingController::class, 'chake_password'])->name('user.setting.chake_password');
         Route::post('setting/email/pass', [SettingController::class, 'password'])->name('user.setting.password');
         Route::post('setting/email/code', [SettingController::class, 'code'])->name('user.setting.code');
         Route::get('setting/email/verification', [SettingController::class, 'verification'])->name('user.setting.verification');
         Route::post('setting/email/check', [SettingController::class, 'check_verification'])->name('user.setting.check_verification');
         Route::get('setting/email/tryagan', [SettingController::class, 'tryAgan'])->name('user.setting.tryAgan');
         Route::get('setting/identity', [SettingController::class, 'identity'])->name('user.setting.identity');

         //Fot freelance only
         Route::group([ 'middleware' => 'profile'], function () {
             //work submitting
             Route::get('submitting/{id}', [SubmittingController::class, 'index'])->name('user.submitting');
             Route::post('delivery/{id}', [SubmittingController::class, 'submitting'])->name('user.delivery');
            });

        //Proposal
        Route::get('profile/Proposal/{id}', [ProposalController::class, 'index'])->name('user.Proposal');
        Route::post('Proposal/reject/{id}', [ProposalController::class, 'reject'])->name('user.Proposal.reject');

        //message
        Route::get('message/{id}', [MessageController::class, 'index'])->name('user.message');
        Route::post('message/save', [MessageController::class, 'sendMessage'])->name('user.message.save');

        //Proposal_decision
        Route::post('acsept/Proposal/', [Proposal_decisionController::class, 'create'])->name('user.Proposal_decision.create');
        Route::get('acsept/Proposal/{id}', [Proposal_decisionController::class, 'index'])->name('user.Proposal_decision.index');
        Route::get('acsept/Proposal/freelance/{id}', [Proposal_decisionController::class, 'freelance'])->name('user.Proposal_decision.freelance');
        //dashboard
        Route::get('dashboard/user', [DashboardController::class, 'userDashboard'])->name('user.userDashboard');
        Route::get('dashboard/freelance', [DashboardController::class, 'freelance'])->name('user.freelanceDashboard');
        //Rate
        Route::get('rate/', [RateController::class, 'index'])->name('user.rate');
        Route::post('rate/', [RateController::class, 'create'])->name('user.rate.create');






    });

});











// Route Admins

Route::group(['prefix' => 'admin','middleware' => 'auth:admins'], function () {

   Route::get('/home', [AdminController::class, 'index'])->name('admin.home');
   Route::post('/logout', [AdminController::class, 'logout'])->name('admin.logout');


});



Route::get('admin/login', [AdminController::class, 'login'])->name('admin.login')->middleware('RedirectIfLogin');


Route::post('/check_login', [AdminController::class, 'check_login'])->name('admin.check.login');

<?php

namespace App\Http\Middleware\front;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class UserProfile
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
       if (Auth::guard('web')->check() && Auth::guard('web')->user()->account_type == 1) {
            
        return Redirect()->route('user.profile.index');
       }
        return $next($request);
    }
}

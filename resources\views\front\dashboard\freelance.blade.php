@extends('front.layout.layout')

@section('body')
    <link rel="stylesheet" href="{{ asset('front/asset/css/dashboard.css') }}">
    <style>
        .view-toggle {
            display: flex;
            background: #f0f0f0;
            border-radius: 8px;
            overflow: hidden;
        }

        .view-toggle button {
            padding: 10px 15px;
            border: none;
            background: none;
            cursor: pointer;
            transition: all 0.3s;
        }

        .view-toggle button.active {
            background: var(--primary);
            color: white;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin: 25px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
            text-align: center;
            transition: transform 0.3s;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card h3 {
            font-size: 2.2rem;
            margin-bottom: 8px;
            color: var(--primary);
        }

        .stat-card p {
            color: var(--dark);
            font-weight: 600;
        }

        /* تصميم البطاقات للهواتف */
        .cards-view {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 20px;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .project-name {
            font-weight: 700;
            font-size: 1.2rem;
            color: var(--dark);
        }

        .project-amount {
            font-weight: 700;
            color: var(--primary);
            font-size: 1.1rem;
        }

        .card-body {
            margin-bottom: 15px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .info-label {
            color: #777;
            font-weight: 600;
        }

        .info-value {
            font-weight: 600;
            text-align: left;
        }

        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 600;
            display: inline-block;
            min-width: 90px;
            text-align: center;
        }

        .bg-success {
            background-color: var(--success);
            color: #fff;
        }

        .bg-danger {
            background-color: var(--danger);
            color: #fff;
        }

        .bg-warning {
            background-color: var(--warning);
            color: #000;
        }

        .bg-secondary {
            background-color: var(--secondary);
            color: #fff;
        }

        .action-btn {
            background: none;
            border: none;
            color: var(--primary);
            cursor: pointer;
            font-size: 1.1em;
            transition: color 0.3s;
            padding: 5px;
        }

        .action-btn:hover {
            color: #2980b9;
        }

        /* تصميم الجدول للشاشات المتوسطة والكبيرة */
        .table-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-top: 20px;
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 800px;
        }

        th,
        td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }

        th {
            background-color: var(--primary);
            color: #fff;
            font-weight: 600;
            padding: 18px 15px;
        }

        th i {
            margin-left: 5px;
        }

        tr:hover {
            background-color: #f9f9f9;
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 25px;
            gap: 8px;
        }

        .pagination button {
            padding: 10px 16px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            font-family: 'Cairo', sans-serif;
        }

        .pagination button:hover {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .pagination button.active {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        /* إخفاء الجدول افتراضيًا وعرض البطاقات */
        .table-view {
            display: block;
        }

        .cards-view {
            display: none;
        }

        @media (max-width: 1200px) {
            .container {
                max-width: 100%;
            }

            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 992px) {
            header {
                flex-direction: column;
                align-items: stretch;
            }

            .search-filter {
                width: 100%;
                justify-content: space-between;
            }

            .search-box {
                flex: 1;
            }

            .search-box input {
                width: 100%;
            }

            .table-view {
                display: none;
            }

            .cards-view {
                display: grid;
            }
        }

        @media (max-width: 768px) {
            .stats {
                grid-template-columns: 1fr;
            }

            .cards-view {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            h2 {
                font-size: 1.5rem;
            }

            .search-filter {
                flex-direction: column;
            }

            .filter select {
                width: 100%;
            }

            .badge {
                font-size: 12px;
                padding: 4px 8px;
                min-width: 70px;
            }
        }

        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background-color: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
            z-index: 100;
        }

        .tooltip:hover::after {
            opacity: 1;
            visibility: visible;
        }
    </style>


    @include('include.alert')

    <main class="container">
        <div class="dashboard-container">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <h2>لوحة إدارة المشاريع</h2>
                <p>إدارة ومتابعة جميع مشاريعك من مكان واحد</p>
            </div>

            <!-- Statistics Summary Section -->
            <div class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card total">
                        <div class="stat-icon">📊</div>
                        <div class="stat-info">
                            <h3 id="total-projects">{{ $status['all'] }}</h3>
                            <p>إجمالي المشاريع</p>
                        </div>
                    </div>

                    <div class="stat-card completed">
                        <div class="stat-icon">✅</div>
                        <div class="stat-info">
                            <h3 id="completed-projects">{{ $status['complat'] }}</h3>
                            <p>مشاريع مكتملة</p>
                        </div>
                    </div>

                    <div class="stat-card in-progress">
                        <div class="stat-icon">⚡</div>
                        <div class="stat-info">
                            <h3 id="progress-projects">{{ $status['close'] }}</h3>
                            <p>مشاريع قيد التنفيذ</p>
                        </div>
                    </div>

                    <div class="stat-card open">
                        <div class="stat-icon">📋</div>
                        <div class="stat-info">
                            <h3 id="open-projects">{{ $status['open'] }}</h3>
                            <p>مشاريع مفتوحة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project Tabs Section -->
            <div class="projects-section">
                <div class="tabs-container">
                    <div class="tab-buttons">
                        <button class="tab-button active" data-tab="open">
                            المشاريع المفتوحة <span class="tab-count" id="open-count">{{ $status['open'] }}</span>
                        </button>
                        <button class="tab-button" data-tab="progress">
                            قيد التنفيذ <span class="tab-count" id="progress-count">{{ $status['close'] }}</span>
                        </button>
                        <button class="tab-button" data-tab="completed">
                            المكتملة <span class="tab-count" id="completed-count">{{ $status['complat'] }}</span>
                        </button>
                    </div>

                    <!-- Open Projects Tab -->
                    <div class="tab-content active" id="open-tab">
                        <div class="projects-grid" id="open-projects-grid">
                            {{--  --}}
                            @foreach ($openWorks as $openWork)
                                <div class="project-card open-project">
                                    <div class="project-header">
                                        <h3 class="project-title">{{ $openWork->category_name }}</h3>
                                        <span class="project-status open">مفتوح</span>
                                    </div>
                                    <p class="project-description">{{ $openWork->name }}
                                    </p>
                                    <div class="project-details">
                                        <div class="detail-item">
                                            <span class="detail-label">الميزانية:</span>
                                            <span class="detail-value">{{ $openWork->category_name }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">المدة:</span>
                                            <span class="detail-value">{{ $openWork->expiration_date }} </span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">العروض:</span>
                                            <span class="detail-value">{{ $openWork->proposals_count }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">تاريخ النشر:</span>
                                            <span class="detail-value">
                                                {{ \Carbon\Carbon::parse($openWork->created_at)->format('Y-m-d') }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="project-actions">
                                        <form action="{{ route('user.work.display', $openWork->id) }}" method="get">

                                            <button class="primary-button">عرض العروض</button>
                                        </form>
                                        <form action="{{ route('user.work.edit', $openWork->id) }}" method="get">

                                            <button class="outline-button">تعديل</button>
                                        </form>
                                    </div>
                                </div>
                            @endforeach
                            {{--  --}}
                        </div>
                    </div>
                    {{-- 'u.name as ',
                 'u.id as user_id',

                 'w.',
                 'w.',
                 'w.',
                 'w.',
                 'p.proposal_id' --}}
                    <!-- In Progress Projects Tab -->
                    <div class="tab-content" id="progress-tab">
                        <div class="projects-grid" id="progress-projects-grid">
                            {{--  --}}
                            @foreach ($offers as $offer)
                                <div class="project-card progress-project">
                                    <div class="project-header">
                                        <h3 class="project-title"> {{ $offer->name }}</h3>
                                        <span class="project-status in-progress">قيد التنفيذ</span>
                                    </div>
                                    <p class="project-description" id="incres-text"> {{ $offer->description }}</p>
                                    <div class="project-details">
                                        <div class="detail-item">
                                            <span class="detail-label">الميزانية:</span>
                                            <span class="detail-value">{{ $offer->amount }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">المستقل:</span>
                                            <span class="detail-value"> {{ $offer->client_name }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">تاريخ البدء:</span>
                                            <span class="detail-value">
                                                {{ \Carbon\Carbon::parse($offer->created_at)->format('Y-m-d') }}
                                            </span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">عدد ايام التنفيذ :</span>
                                            <span class="detail-value">{{ $offer->expiration_date }}

                                            </span>
                                        </div>
                                    </div>
                                    {{-- <div class="progress-section">
                                    <div class="progress-info">
                                        <span>التقدم: 65%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 65%"></div>
                                    </div>
                                </div> --}}
                                    <div class="project-actions">
                                        <form action="{{ route('user.Proposal_decision.index', $offer->id) }}"
                                            method="GET">
                                            <button class="primary-button">عرض التفاصيل</button>
                                        </form>
                                        <form action="{{ route('user.message', $offer->user_id) }}" method="GET">
                                            <button class="secondary-button">التواصل</button>
                                        </form>
                                    </div>
                                </div>
                            @endforeach
                            {{--  --}}
                        </div>
                    </div>

                    {{-- 'u.name as ',
                'u.id as user_id',
                'w.name as work_name',
                'w.amount',
                'w.',
                'w.',
                'w.created_at',
                'p.proposal_id',
                'p.' --}}
                    <!-- Completed Projects Tab -->
                    <div class="tab-content" id="completed-tab">
                        <div class="projects-grid" id="completed-projects-grid">
                            @foreach ($complatWork as $complatWorks)
                                <div class="project-card completed-project">
                                    <div class="project-header">
                                        <h3 class="project-title"> {{ $complatWorks->name }}</h3>
                                        <span class="project-status completed">مكتمل</span>
                                    </div>
                                    <p class="project-description">{{ $complatWorks->description }}</p>
                                    <div class="project-details">
                                        <div class="detail-item">
                                            <span class="detail-label">الميزانية:</span>
                                            <span class="detail-value">{{ $complatWorks->amount }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">المستقل:</span>
                                            <span class="detail-value"> {{ $complatWorks->client_name }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">تاريخ الإكمال:</span>
                                            <span class="detail-value">
                                                {{ \Carbon\Carbon::parse($complatWorks->updated_at)->format('Y-m-d') }}

                                            </span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">المدة:</span>
                                            <span class="detail-value">{{ $complatWorks->expiration_date }}</span>
                                        </div>
                                    </div>
                                    <div class="rating-section">
                                        <span class="rating-label">التقييم:</span>
                                        <span class="rating-stars">
                                            @php
                                                $fullStars = floor($complatWorks->rating);
                                                $halfStar = $complatWorks->rating - $fullStars >= 0.5;

                                                $emptyStars = 5 - $fullStars - ($halfStar ? 1 : 0);

                                                for ($i = 0; $i < $fullStars; $i++) {
                                                    echo '⭐';
                                                }
                                                for ($i = 0; $i < $emptyStars; $i++) {
                                                    echo '★';
                                                }

                                            @endphp
                                        </span>
                                        <span class="rating-value">5/{{ $complatWorks->rating }}</span>
                                    </div>
                                    <div class="project-actions">
                                        {{-- <button class="outline-button" onclick="viewProjectDetails(8)">عرض التفاصيل</button> --}}
                                        <button class="secondary-button" onclick="downloadInvoice(8)">تحميل
                                            الفاتورة</button>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container">
            <!-- عرض البطاقات (للشاشات الصغيرة) -->
            <div class="cards-view">

                @foreach ($myProposals as $myProposal)
                    <div class="card">
                        <div class="card-header">
                            <div class="project-name"> {{ $myProposal->work_name }}</div>
                            <div class="project-amount">{{ $myProposal->amount }} $</div>
                        </div>
                        <div class="card-body">
                            <div class="info-row">
                                <span class="info-label">اسم العميل:</span>
                                <span class="info-value"> {{ $myProposal->client_name }}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">حالة القرار:</span>

                                @if ($myProposal->proposal_status == 1)
                                    <td><span class="badge bg-warning">قيد الانتظار </span></td>
                                @elseif($myProposal->proposal_status == 0)
                                    <td><span class="badge bg-danger">مستبعد</span></td>
                                @elseif($myProposal->proposal_status == 3)
                                    <td><span class="badge bg-success">مكتمل</span></td>
                                @else
                                    <td><span class="badge bg-success">مقبول</span></td>
                                @endif
                            </div>
                            <div class="info-row">
                                <span class="info-label">حالة المشروع:</span>


                                @if ($myProposal->work_status == 1)
                                    <span class="badge bg-success">مفتوع</span>
                                @elseif ($myProposal->work_status == 2)
                                    <span class="badge bg-secondary">قيد التنفيذ</span>
                                @else
                                    <span class="badge bg-success"> مكتمل</span>
                                @endif

                            </div>
                        </div>
                        <div class="card-footer">
                            <div>
                                <button class="action-btn" data-tooltip="عرض التفاصيل"><i
                                        class="fas fa-eye"></i></button>
                                <button class="action-btn" data-tooltip="تعديل"><i class="fas fa-edit"></i></button>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            <div class="messages-section">
                <div class="section-header-messages">
                    <h2>الرسائل والإشعارات</h2>
                    <a href="#" class="view-all-link">عرض الكل</a>
                </div>
                <div class="messages-list" id="messages-list">
                    <div class="messages-list" id="app">

                        @foreach ($proposals as $proposal)
                            <a href="{{route('user.Proposal_decision.freelance', $proposal->id)}}">
                                <div class="message-item @if ($proposal->is_read == 0) unread @endif">
                                    <div class="message-header">
                                        <span class="message-sender">{{ $proposal->name }} {{ $proposal->is_read }}
                                        </span>
                                        <span class="message-time">

                                            {{ $proposal->is_read }}

                                            <time-ago
                                                time="{{ \Carbon\Carbon::parse($proposal->created_at)->toIso8601String() }}"></time-ago>

                                        </span>
                                    </div>
                                    <p class="message-content">تم قبول طلب اضغط من اجل التفاصيل</p>
                                </div>
                            </a>
                        @endforeach


                    </div>
                </div>
            </div>

            <!-- عرض الجدول (للشاشات الكبيرة) -->
            <div class="table-view">
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th><i class="fas fa-project-diagram"></i> اسم المشروع</th>
                                <th><i class="fas fa-dollar-sign"></i> المبلغ</th>
                                <th><i class="fas fa-user"></i> اسم العميل</th>
                                <th><i class="fas fa-clipboard-check"></i> حالة القرار</th>
                                <th><i class="fas fa-tasks"></i> حالة المشروع</th>
                                <th><i class="fas fa-cog"></i> الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($myProposals as $myProposal)
                                <tr>
                                    <td data-tooltip="تصميم موقع إلكتروني متكامل"> {{ $myProposal->work_name }}</td>
                                    <td>{{ $myProposal->amount }} $</td>
                                    <td> {{ $myProposal->client_name }}</td>

                                    @if ($myProposal->proposal_status == 1)
                                        <td><span class="badge bg-warning">قيد الانتظار </span></td>
                                    @elseif($myProposal->proposal_status == 0)
                                        <td><span class="badge bg-danger">مستبعد</span></td>
                                    @elseif($myProposal->proposal_status == 3)
                                        <td><span class="badge bg-success">مكتمل</span></td>
                                    @else
                                        <td><span class="badge bg-success">مقبول</span></td>
                                    @endif

                                    @if ($myProposal->work_status == 1)
                                        <td><span class="badge bg-success">مفتوع</span></td>
                                    @elseif ($myProposal->work_status == 2)
                                        <td><span class="badge bg-secondary">قيد التنفيذ</span></td>
                                    @else
                                        <td><span class="badge bg-success"> مكتمل</span></td>
                                    @endif

                                    <td>
                                        <button class="action-btn" data-tooltip="عرض التفاصيل"><i
                                                class="fas fa-eye"></i></button>
                                        <button class="action-btn" data-tooltip="تعديل"><i
                                                class="fas fa-edit"></i></button>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="pagination">

            </div>
        </div>
    </main>
@section('scriptFile')
    <script src="{{ asset('front/asset/js/user-dashboard.js') }}"></script>
    <script src="{{ asset('front/asset/js/menu-toggle.js') }}"></script>
    <script src="{{ asset('front/asset/js/text.js') }}"></script>
@endsection
@endsection



{{-- proposal_id	26
proposal_status	1
work_name	"متجر 3"
amount	"20000"
client_name	"احمد"
decision_status	null
work_status	1 --}}

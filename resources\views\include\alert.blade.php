  <style>


    .custom-alert {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 25px;
      border-radius: 12px;
      color: #fff;
      min-width: 250px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
      display: none;
      justify-content: space-between;
      align-items: center;
      z-index: 9999;
      transition: 0.3s ease;
    }

    .custom-alert.show {
      display: flex;
    }

    .success {
      background-color: #28a745;
    }

    .error {
      background-color: #dc3545;
    }

    .info {
      background-color: #17a2b8;
    }

    .warning {
      background-color: #ffc107;
      color: #333;
    }

    .close-alert {
      margin-left: 15px;
      cursor: pointer;
      font-weight: bold;
      font-size: 20px;
    }

    button {
      padding: 10px 20px;
      margin: 10px;
      font-size: 16px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: 0.3s;
    }

    .btn-success { background-color: #28a745; color: white; }
    .btn-error { background-color: #dc3545; color: white; }
    .btn-info { background-color: #17a2b8; color: white; }
    .btn-warning { background-color: #ffc107; color: #333; }
  </style>
 <body>
  
@if(session('success') || session('error') || $errors->any())
  <div class="custom-alert show {{ session('success') ? 'success' : (session('error') ? 'error' : 'warning') }}" id="alertBox">
    <span class="alert-message" id="alertMessage">
      @if(session('success'))
        {{ session('success') }}
      @elseif(session('error'))
        {{ session('error') }}
      @elseif($errors->any())
        {{ $errors->first() }}
      @endif
    </span>
    <span class="close-alert" onclick="closeAlert()">×</span>
  </div>
@endif


  <script>
    function showAlert(message, type) {
      const alertBox = document.getElementById('alertBox');
      const alertMessage = document.getElementById('alertMessage');

      alertMessage.textContent = message;
      alertBox.className = 'custom-alert show ' + type;

      // إخفاء التنبيه تلقائياً بعد 3 ثوانٍ
      setTimeout(closeAlert, 3000);
    }

    function closeAlert() {
      const alertBox = document.getElementById('alertBox');
      alertBox.classList.remove('show');
    }
  </script>


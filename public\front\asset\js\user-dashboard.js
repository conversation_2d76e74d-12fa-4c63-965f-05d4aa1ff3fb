        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.getAttribute('data-tab');

                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));

                // Add active class to clicked button and corresponding content
                button.classList.add('active');
                document.getElementById(`${tabName}-tab`).classList.add('active');
            });
        });

        // Action functions (placeholder implementations)
        function viewProject(id) {
            window.location.href = `../projects/view-proposals.html?project_id=${id}`;
        }

        function editProject(id) {
            window.location.href = `../projects/edit-project.html?project_id=${id}`;
        }

        function viewProjectDetails(id) {
            window.location.href = `../projects/project-details.html?project_id=${id}`;
        }

        function contactFreelancer(id) {
            window.location.href = `../messages/chat.html?project_id=${id}`;
        }

        function downloadInvoice(id) {
            alert(`تحميل فاتورة المشروع رقم ${id}`);
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadProjects();
        });

        // Toggle menu for mobile
        const toggleBtn = document.querySelector('.menu-toggle');
        const nav = document.querySelector('.main-nav');

        toggleBtn.addEventListener('click', () => {
            nav.classList.toggle('active');
        });

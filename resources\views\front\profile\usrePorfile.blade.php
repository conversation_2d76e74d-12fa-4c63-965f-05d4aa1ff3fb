 @extends('front.layout.layout')
 @section('title')
     <title> الملف الشخصي</title>
 @endsection

 @section('body')

     @vite('resources/js/app.js')

     @include('include.alert')

     <main class="container">
         <div class="profile-overview">
             <div class="profile-image-container">
                 @if (Auth::guard()->user()->verified == 1)
                     <div class="profile-activity">
                         <img class="activity" src="{{ asset('assets/imgs/icons/activity-03.png') }}" alt="activity">
                     </div>
                 @endif
                 <img src="{{ asset(Auth::guard()->user()->avater) }}" alt="الصورة الشخصية" id="profile-image">
             </div>

             <div class="profile-name-container">
                 <h2 id="user-name"> {{ Auth::guard()->user()->name }}</h2>
                 <h4 id="job" style="color: #333; "> صاحب عمل</h4>
             </div>

             <div class="profile-tabs">
                 <button class="tab-button active" data-tab="info">المعلومات الشخصية</button>
                 <button class="tab-button" data-tab="portfolio"> الأعمال</button>
                 <button class="tab-button" data-tab="projects">العروض</button>
                 <button class="tab-button" data-tab="reviews">صندوق الوارد

                 </button>
             </div>
             <div class="tab-content" id="info-tab">
                 <div class="profile-info-section">
                     <h3>معلومات الاتصال</h3>
                     <div class="info-item">
                         <div class="input">
                             <div class="info-label">البريد الإلكتروني</div>
                             <div class="info-value" id="user-email"> {{ Auth::guard()->user()->email }}</div>
                         </div>
                         <div class="stutes">
                             <span>test</span>
                         </div>
                     </div>
                     <div class="info-item">
                         <div class="input">
                             <div class="info-label">رقم الهاتف</div>
                             <div class="info-value" id="user-phone"> {{ Auth::guard()->user()->phon }}</div>
                         </div>
                         <div class="stutes">
                             <span>test</span>
                         </div>
                     </div>
                     <div class="info-item">
                         <div class="input">
                             <div class="info-label">الموقع</div>
                             <div class="info-value" id="user-location"> {{ Auth::guard()->user()->address }}</div>
                         </div>
                     </div>
                 </div>

                 <div class="profile-info-section">
                     <h3>نبذة عني</h3>
                     <p class="user-bio" id="user-bio">
                         {{ Auth::guard()->user()->bio }}
                     </p>
                 </div>
             </div>

             <div class="tab-content hidden" id="portfolio-tab">
                 <div class="portfolio-header">
                     <h3>معرض الأعمال</h3>
                     <a id="" href="{{ route('user.work.create') }}" class="add-portfolio-btn btn">إضافة عمل جديد</a>
                 </div>

                 <div class="" id="portfolio-items">
                     <div class="portfolio-item" style="margin-bottom:20px">
                         {{-- <div class="portfolio-image">

                        </div> --}}
                         @foreach ($works as $work)
                             <div class="portfolio-info" style="border-bottom: 1px solid #33333324;">
                                 <h4>{{ $work->name }} </h4>
                                 <p> {{ $work->description }} </p>
                                 <div class="control">
                                     <a class="btn btn-primary" href="{{ route('user.work.display', $work->id) }}"
                                         style="  background-color: #1877F2 !important;"href="">عرض</a>
                                     <a class="btn btn-edit"href="{{ route('user.work.edit', $work->id) }}"
                                         style=" background-color: #FF9800 ">تعديل</a>
                                     <a class="btn btn-danger"href="{{ route('user.work.delete', $work->id) }}"
                                         style="   background-color: #D32F2F !important;">حذف</a>
                                 </div>
                                 @if ($work->status == 1)
                                 <span>الحاله :</span> مفتوح

                                 @elseif($work->status == 3)
                                 <span>الحاله :</span> مكتمل

                                 @else
                                 <span>الحاله :</span> قيد التنفيذ

                                 @endif
                             </div>
                         @endforeach
                     </div>

                 </div>

                 <!-- Portfolio Modal -->
                 <div id="portfolio-modal" class="portfolio-modal">
                     <div class="portfolio-modal-content">
                         <span class="close-modal">&times;</span>
                         <div class="portfolio-modal-body">
                             <div class="portfolio-modal-image">
                                 <img id="modal-image" src="" alt="">
                             </div>
                             <div class="portfolio-modal-details">
                                 <h3 id="modal-title"></h3>
                                 <p id="modal-description"></p>
                                 <div class="portfolio-meta">
                                     <div class="meta-item">
                                         <span class="meta-label">التاريخ:</span>
                                         <span id="modal-date"></span>
                                     </div>
                                     <div class="meta-item">
                                         <span class="meta-label">العميل:</span>
                                         <span id="modal-client"></span>
                                     </div>
                                     <div class="meta-item">
                                         <span class="meta-label">الفئة:</span>
                                         <span id="modal-category"></span>
                                     </div>
                                 </div>
                             </div>
                         </div>
                     </div>
                 </div>
             </div>

             <div class="tab-content hidden" id="projects-tab">
                 @empty($proposals)
                     <div class="empty-state">
                         <div class="empty-icon">📁</div>
                         <h3>لا توجد مشاريع حالية</h3>
                         <p>لم تقم بإضافة أي مشاريع بعد.</p>
                     </div>
                 @endempty

                 <section class="container">
                     <h2 class="section-title">العروض </h2>
                     <div class="faq">
                         <div id="app2" class="container mx-auto p-5">
                             <proposals-component :number="{{ Auth::guard()->user()->id }}"></proposals-component>
                         </div>
                     </div>
                 </section>
             </div>
             <div class="tab-content hidden" id="reviews-tab">
                @empty($inboxs)
                <div class="empty-state">
                    <div class="empty-icon">⭐</div>
                    <h3>صندوق الوارد</h3>
                    <p>لم تحصل على أي تقييمات بعد.</p>
                </div>
                @endempty
                 <div>
                          <div id="app">
                     <div class="inbox">
                         <!-- محادثة 1 -->

                         @foreach ($inboxs as $inbox )
                                     <div class="chat-item">
                             <img src="{{asset($inbox->avater)}}" class="avatar" alt="user">
                             <div class="chat-info">
                                <a href="{{route('user.message', $inbox->user_id)}}">
                                 <div class="chat-header">
                                     <span class="chat-name"> {{$inbox->name}}</span>
                                     <div style="    display: flex align-items: center; flex-direction: column;">
                                         <span class="chat-time"> <time-ago time="{{ \Carbon\Carbon::parse($inbox->last_time)->toIso8601String() }}"></time-ago></span>
                                         @empty(!$inbox->unread_count)

                                         <span class="chat-count">{{$inbox->unread_count}}</span>
                                         @endempty
                                     </div>
                                 </div>
                                 <div class="chat-message"> {{$inbox->last_message}}</div>
                                 </a>
                             </div>
                         </div>
                         @endforeach
                     </div>
                     </div>
                 </div>
             </div>

             <div class="profile-actions">
                 <a href="{{ route('user.userDashboard') }}" class="edit-profile-btn">لوحة التحكم</a>
             </div>
            <div class="profile-actions">
                <a href="{{ route('user.setting') }}" class="edit-profile-btn"> الاعدادات </a>
            </div>
         </div>
     </main>


 @section('scriptFile')
     <script src="{{ asset('front/asset/js/profile.js') }}"></script>
 @endsection
@endsection

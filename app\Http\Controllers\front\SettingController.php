<?php

namespace App\Http\Controllers\front;

use App\Http\Controllers\Controller;
use App\Models\front\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class SettingController extends Controller
{
    public function index() {
        return view('front.setting.index');
    }
    
    public function work() {
        return view('front.setting.work');
        
    }

    public function workUpdate(Request $request) {

        $user =User::where( 'id',Auth::user()->id)->first();

        $user->update([
            'categorie_id' =>  $request->department_id,
            'job_id' =>  $request->job_id
        ]);

        return  Redirect()->route('user.profile.index')->with('success','تم التحديث بنجاح');

    }

    public function account_type() {
        return view('front.setting.account');
        
    }
    public function accountType_update(Request $request) {

        $user =User::where( 'id',Auth::user()->id)->first();

        $user->update([
            'account_type' =>  $request->account_type
        ]);

        return  Redirect()->route('user.profile.index')->with('success','تم التحديث بنجاح');
        
    }

    public function chake_password() {

        return view('front.setting.email');

    }

    public function password(Request $request) {

        $user =User::where( 'id',Auth::user()->id)->first();

        if(Hash::check($request->password,$user->password)){
            
          $user =User::where( 'email',)->first();

            return view('front.setting.changeEmail');
        }
    
        return Redirect()->back()->with('error','   كلمة المرور غير صحيحة');

    }

    public function code(Request $request) {

          $user =User::where( 'email', $request->email)->where('verification_email',1)->first();

          $userpass =User::where( 'id', Auth::user()->id)->first();

         if($user) {

            return Redirect()->route('user.setting')->with('error',' البريد مسجل مسبقا');

         }

         $randomcode = rand(100000, 999999); 

        $userpass->update([
            'tamp_email' =>   $request->email,
            'code' =>   $randomcode,
        ]);

        return redirect()->route('user.setting.verification');

    }

    public function verification() {
  
        return view('front.setting.verification');
    }

    public function check_verification(Request $request){


         $user = User::where('code', $request->code)
            ->where('id', Auth::id())
            ->first();


         if($user) {

            $user->update([
                'email' =>  $user->tamp_email,
                'tamp_email' =>  ''
            ]);
            
        return  Redirect()->route('user.profile.index')->with('success','تم التحديث بنجاح');

        }
    
        return  Redirect()->route('user.setting.tryAgan')->with('error','الكود غير صحيح');

    }

    public function tryAgan(){

          return view('front.Auth.verification-email');
    }

    public function identity() {
        
        return view('front.setting.identity');
        
    }
}
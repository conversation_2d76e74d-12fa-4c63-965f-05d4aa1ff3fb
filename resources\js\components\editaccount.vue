<template>
  <div>
       <div v-if="userJob">{{ userJob['departments'] ['name']}} </div>
            <h3>معلومات الحساب</h3>
            <div class="info-item-edit">
                <div class="info-label"> تعديل عدد سنوات الخبرة</div>
                <div class="info-value" id="user-email"><input type="number" name="experince" value=""></div> 
            </div>
           
            <div class="info-item-edit">
                <div class="info-label">تعديل الموسمي الوظيفي   </div>
                <div class="info-value" id="user-email"><input type="text" name="job_id" value=""></div> 
                    </div>
            <div class="info-item-edit">
                <div class="info-label">تعديل  نوع الحساب </div>
                <div class="info-value" id="user-email">
                    <select>
                        <option value="1" selected>ابحث عن عمل</option>
                        <option value="0" >صاحب عمل</option>
                    </select>
                </div> 
            </div> 

                      <div class="form-group"> 
            <label for="department">اختر القسم</label>
                <select v-model="job_id" @change="getJobs"  name="department_id" >
                    <option select>test</option>

                     <option  selected v-if="userJob" :value="userJob['departments']['id']"> {{userJob['departments']['name']}}</option>
                     <option v-for="categories in categorie" :key="categories.id" :value="categories.id">{{  categories.name}}</option>
                </select>
            </div>

            <div class="form-group">
                <label for="job">اختر الوظيفة</label>
                 <select  name="job_idj"  required>
                    <option select>test</option>
                    <option v-for="categories in jobs" :key="categories.id" :value="categories.id">{{  categories.name}}</option>
                </select> 
            </div>
          {{jobs}}
  </div>
</template>

<script>
export default {
 
  data() {
    return {
    categorie : [], 
    job_id : 0,
    jobs :[],
    userJob :'' ,
    userCategorie :'' ,

    };
  },    
  methods:{
    getJobs () {
          fetch('http://127.0.0.1:8000/get-jobs/' +this.job_id)
        .then(res => res.json())
        .then(data => this.jobs = data) 
        .catch(err =>console.log(err.message));

        console.log(this.job_id)
       
    }
  },
 mounted (){
    

    fetch('http://127.0.0.1:8000/categorie')
        .then(res => res.json())
        .then(data => this.job_id = data.id) 
        .catch(err =>console.log(err.message));


        fetch('http://127.0.0.1:8000/Auth/job')
        .then(res => res.json())
        .then(data => this.userJob = data) 
        .catch(err =>console.log(err.message));
        
    fetch('http://127.0.0.1:8000/categorie')
        .then(res => res.json())
        .then(data => this.categorie = data) 
        .catch(err =>console.log(err.message));


  },

}
</script>

<style>

</style>
<?php

namespace App\Http\Controllers\front;

use App\Http\Controllers\Controller;
use App\Models\Conversation;
use App\Models\front\Message;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MessageController extends Controller
{
public function index($id)
{
    // جلب المحادثة كاملة
    $messages = Message::where(function ($q) use ($id) {
            $q->where('sender_id', Auth::id())
              ->where('receiver_id', $id);
        })
        ->orWhere(function ($q) use ($id) {
            $q->where('sender_id', $id)
              ->where('receiver_id', Auth::id());
        })
        ->join('users as sender', 'messages.sender_id', '=', 'sender.id')
        ->join('users as receiver', 'messages.receiver_id', '=', 'receiver.id')
        ->select(
            'messages.id as message_id',
            'messages.message',
            'messages.created_at',
            'sender.id as sender_id',
            'sender.name as sender_name',
            'sender.avater',
            'receiver.id as receiver_id',
            'receiver.name as receiver_name'
        )
        ->orderBy('messages.created_at', 'asc')
        ->get();

    $receiver = $id;

    // تحديث حالة الرسائل الواردة من هذا المستخدم
    $readms = Message::where('sender_id', $id)
        ->where('receiver_id', Auth::id())
        ->where('is_read', 0)
        ->update(['is_read' => 1]);

    return view('front.messages.index', compact('messages', 'receiver'));
}


    // إرسال رسالة جديدة
    public function sendMessage(Request $request)
    {
        $request->validate([
            'receiver_id' => 'required|exists:users,id',
            'message'     => 'required|string|max:1000',
        ]);

        // البحث عن المحادثة بين المرسل والمستقبل أو إنشاء واحدة جديدة
        $conversation = Conversation::where(function($q) use ($request) {

                $q->where('user_one_id', Auth::id())
                  ->where('user_two_id', $request->receiver_id);
            })
            ->orWhere(function($q) use ($request) {
                $q->where('user_one_id', $request->receiver_id)
                  ->where('user_two_id', Auth::id());
            })
            ->first();

        if (!$conversation) {
            $conversation = Conversation::create([
                'user_one_id' => Auth::id(),
                'user_two_id' => $request->receiver_id,
            ]);
        }

        // إضافة الرسالة
        $message = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id'       => Auth::id(),
            'receiver_id'     => $request->receiver_id,
            'message'         => $request->message,
            'is_read' =>0,
            'created_at'=>NOW(),
            'updated_at'=>NOW()
        ]);

      return redirect()->back()->with('success','تم ارسال الرد');
    }
}

const wordLimit = 10;
const textElements = document.querySelectorAll("#incres-text");
textElements.forEach((p, index) => {
  const fullText = p.innerText;
  const words = fullText.split(" ");
  const limitedText = words.slice(0, wordLimit).join(" ") + (words.length > wordLimit ? "..." : "");

  p.setAttribute("data-full", fullText);
  p.setAttribute("data-limited", limitedText);
  p.innerText = limitedText;
});

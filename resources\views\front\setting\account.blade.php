@extends('front.layout.layout')

@section('body')

    @include('include.alert')
    <main class="container">

        <div class="profile-overview">
            <div class="profile-image-container">

                <img id="imagePreview" class="img" src="{{ asset(Auth::guard()->user()->avater) }}" alt="الصورة الشخصية">
            </div>

            <div class="profile-name-container">
                <h4 id="job" style="color: #333; "> {{ Auth::guard()->user()->name }}</h4>
            </div>

            <div class="profile-tabs">
                <button class="tab-button active" data-tab="info">تعديل نوع الحساب </button>

            </div>

            <div class="tab-content" id="info-tab">
                <div class="profile-info-section">

                    <form action="{{ route('user.setting.account.update') }}" method="POST">
                        @csrf
                        <h3>معلومات الحساب</h3>
                        <div class="info-item-edit">
                            <div class="info-label">تعديل نوع الحساب </div>
                            <div class="info-value" id="user-email">
                                <select name="account_type">
                                    <option value="1"@if (Auth::guard()->user()->account_type == 1) selected @endif>ابحث عن عمل
                                    </option>
                                    <option value="0"@if (Auth::guard()->user()->account_type == 0) selected @endif>صاحب عمل
                                    </option>
                                </select>
                            </div>
                            <button class="edit-profile-btn">حفظ التعديلات </button>
                    </form>
                </div>
            </div>
        </div>
    </main>

@section('scriptFile')
    <script src="{{ asset('front/asset/js/menu-toggle.js') }}"></script>
@endsection
@endsection

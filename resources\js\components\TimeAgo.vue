<template>
  <span :title="absoluteTitle">{{ display }}</span>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';

const props = defineProps({
  time: { type: [String, Number, Date], required: true },
  locale: { type: String, default: 'ar' },
  showAbsoluteAfterDays: { type: Number, default: 30 },
  useArabicFriendly: { type: Boolean, default: true }
});

const display = ref('');
let timer;

function toDate(value) {
  if (value instanceof Date) return value;
  if (typeof value === 'number') return new Date(value);
  return new Date(String(value));
}

function timeAgoText(date) {
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  if (props.useArabicFriendly) {
    if (diffSec < 5) return 'الآن';
    if (diffSec < 60) return `منذ ${diffSec} ثانية`;
    if (diffMin < 60) return diffMin === 1 ? 'قبل دقيقة' : `قبل ${diffMin} دقيقة`;
    if (diffHour < 24) return diffHour === 1 ? 'قبل ساعة' : `قبل ${diffHour} ساعة`;
    if (diffDay === 1) return 'أمس';
    if (diffDay < 7) return `قبل ${diffDay} يوم`;
    if (diffDay < 30) {
      const weeks = Math.floor(diffDay / 7);
      return weeks === 1 ? 'منذ أسبوع' : `قبل ${weeks} أسبوع`;
    }
    const months = Math.floor(diffDay / 30);
    if (months < 12) return months === 1 ? 'قبل شهر' : `قبل ${months} شهر`;
    const years = Math.floor(months / 12);
    return years === 1 ? 'قبل سنة' : `قبل ${years} سنة`;
  }
}

function shouldShowAbsolute(date) {
  const now = new Date();
  const diffDay = Math.floor((now - date) / (1000 * 60 * 60 * 24));
  return diffDay >= props.showAbsoluteAfterDays;
}

function absoluteDateString(date) {
  try {
    return new Intl.DateTimeFormat(props.locale, {
      year: 'numeric', month: 'short', day: 'numeric',
      hour: '2-digit', minute: '2-digit'
    }).format(date);
  } catch {
    return date.toLocaleString();
  }
}

function computeNextInterval(date) {
  const now = new Date();
  const diffSec = Math.floor((now - date) / 1000);

  if (diffSec < 60) return 1000; // كل ثانية
  if (diffSec < 3600) return 1000 * 30; // كل 30 ثانية
  if (diffSec < 86400) return 1000 * 60; // كل دقيقة
  return 1000 * 60 * 60; // كل ساعة
}

function update() {
  const date = toDate(props.time);
  if (isNaN(date)) {
    display.value = '';
    return;
  }

  if (shouldShowAbsolute(date)) {
    display.value = absoluteDateString(date);
  } else {
    display.value = timeAgoText(date);
  }
}

function schedule() {
  clearTimeout(timer);
  const date = toDate(props.time);
  const interval = computeNextInterval(date);
  timer = setTimeout(() => {
    update();
    schedule();
  }, interval);
}

onMounted(() => {
  update();
  schedule();
});

onBeforeUnmount(() => {
  clearTimeout(timer);
});

watch(() => props.time, () => {
  update();
  schedule();
});

const absoluteTitle = computed(() => {
  const date = toDate(props.time);
  return isNaN(date) ? '' : date.toString();
});
</script>
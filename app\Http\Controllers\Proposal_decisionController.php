<?php

namespace App\Http\Controllers;

use App\Models\front\Proposal;
use App\Models\front\Work;
use App\Models\Proposal_decision;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Proposal_decisionController extends Controller
{

    public function index($id)
    {


        $decision = Proposal_decision::where("id", $id)->first();

        $data = DB::table('proposal_decisions as d')
            ->join('users', 'users.id', '=', 'd.freelance_id')
            ->join('proposals', 'proposals.id', '=', 'd.proposal_id')
            ->join('works', 'works.id', '=', 'd.work_id')
            ->join('categories as c', 'c.id', '=', 'users.categorie_id')

            ->select('works.id as work_id','works.name as work_name','works.amount','works.expiration_date',

            'd.id as decision_id','d.created_at','d.id','users.id as user_id','users.name as name','users.email',

            'users.avater','c.name as cname')
            ->where('d.freelance_id', $decision->freelance_id)
            ->where('d.proposal_id', $decision->proposal_id)
            ->first();

        $ID = [
            'work_id'=> $data->work_id,
            'freelance_id'=> $data->user_id,
            'decision'=> $data->decision_id,
        ];
        return view('front.ProposalDecision.accepted', compact('data','ID'));

    }
    public function freelance($id)
    {


        $decision = Proposal_decision::where("id", $id)->first();

        $data = DB::table('proposal_decisions as d')
            ->join('users', 'users.id', '=', 'd.user_id')
            ->join('proposals', 'proposals.id', '=', 'd.proposal_id')
            ->join('works', 'works.id', '=', 'd.work_id')
            // ->join('categories as c', 'c.id', '=', 'users.categorie_id')

            ->select('works.id as work_id','works.name as work_name','works.amount','works.expiration_date',

            'd.id as decision_id','d.created_at','d.id','users.id as user_id','users.name as name','users.email',

            'users.avater')
            ->where('d.user_id', $decision->user_id)
            ->where('d.proposal_id', $decision->proposal_id)
            ->first();

        $ID = [
            'work_id'=> 1,
            'freelance_id'=> 1,
            'decision'=> 1,
        ];
        return   view('front.ProposalDecision.freelance', compact('data','ID'));

    }



    public function create(Request $request)
    {


        //user
        $user = Work::where('user_id' , Auth::user()->id)->first();

        $WorkChack = Work::where('id',$request->WD)->where('status', 2)->first();


        if(!$user) {
            return redirect()->back()->with('error','العرض غير متاح');
        }
        if($WorkChack) {
            return redirect()->back()->with('error','العرض غير متاح');
        }

        $decision = Proposal_decision::create([
            'user_id' => Auth::user()->id,
            'freelance_id' => $request->UD,
            'proposal_id' => $request->PD,
            'work_id' => $request->WD,
        ]);


        $Work = Work::where('id', $request->WD)->first();


            // تحديث حالة الرسائل الواردة من هذا المستخدم
         $readms = Proposal::where('work_id', $request->WD)
        // ->where('status', 1)
        ->update(['status' => 0]);

        $Proposal = Proposal::where('id', $request->PD)

        ->where('freelance_id', $request->UD)->first();

        $Work->update([
            'status' => 2,
        ]);
        $Proposal->update([
            'status' => 2,
        ]);

        $id = $decision ->id;

        return  redirect()->route('user.Proposal_decision.index', ['id'=> $id]);

    }
}

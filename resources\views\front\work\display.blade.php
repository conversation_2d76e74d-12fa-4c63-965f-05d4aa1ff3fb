@extends('front.layout.layout')

@section('body')

    @include('include.alert')

    @vite('resources/js/app.js')

    <div class="container">
        <div class="card project-card">
            <div class="card-body">
                <h3 class="card-title"> {{ $work->name }}</h3>
                <p class="card-text"> {{ $work->description }} </p>
                <div id="app">
                    <ul class="list-group list-group-flush mb-4">
                        <li class="list-group-item"><strong>التصنيف:</strong> {{ $work->categoryWork->name }}</li>
                        <li class="list-group-item"><strong>تاريخ الإضافة:</strong><time-ago
                                time="{{ \Carbon\Carbon::parse($work->created_at)->toIso8601String() }}"></time-ago></li>
                        <li class="list-group-item"><strong>طلبات التوظيف:</strong> {{$ProposalCount }}</li>
                        <li class="list-group-item"><strong> الميزانية:</strong> {{ $work->amount }}</li>
                        <li class="list-group-item"><strong> الموقع:</strong> {{ $work->place }}</li>
                        <li class="list-group-item"><strong> عدد ايام التسليم :</strong> {{ $work->expiration_date }}</li>
                        <li class="list-group-item"><strong>الحالة :</strong>

                            @if ($work->status == 1)
                                مفتوح
                            @elseif ($work->status == 3)
                            مكتمل
                            @else
                            قيد التنفيذ
                            @endif
                </div>
                </li>
                {{-- <li class="list-group-item"><strong>المدة:</strong> ٦ أسابيع</li> --}}
                </ul>

                @if (!$check or $work->status =! 3)
                <div class="d-flex justify-content-between flex-wrap action-buttons">
                    <a href="{{ route('user.work.edit', $work->id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>تعديل المشروع
                    </a>
                    <a href="{{ route('user.work.delete', $work->id) }}" class="btn btn-danger">
                        <i class="fas fa-trash-alt me-2"></i> حذف
                    </a>

                    <a href="{{ route('user.projects.redirect') }}" class="btn btn">
                        <i class="fas fa-arrow-right me-2"></i>رجوع للمعرض
                    </a>
                @endif
                </div>
            </div>
        </div>
    </div>


@section('scriptFile')
    <script src="{{ asset('front/asset/js/menu-toggle.js') }}"></script>
@endsection
@endsection

// Toggle menu for mobile
const toggleBtn = document.querySelector('.menu-toggle');
const nav = document.querySelector('.main-nav');

toggleBtn.addEventListener('click', () => {
    nav.classList.toggle('active');
});

// Document type selection
const documentTypes = document.querySelectorAll('input[name="document-type"]');
const passportOption = document.getElementById('passport');
const backUploadSide = document.querySelector('.upload-side:nth-child(2)'); // الوجه الخلفي container

documentTypes.forEach(type => {
    type.addEventListener('change', function() {
        if (this.value === 'passport') {
            backUploadSide.style.opacity = '0.5';
            backUploadSide.style.pointerEvents = 'none';
            // إفراغ صورة الوجه الخلفي إذا كانت موجودة
            clearFile(backInput, backPreview, backPlaceholder);
        } else {
            backUploadSide.style.opacity = '1';
            backUploadSide.style.pointerEvents = 'auto';
        }
        checkFormValidity();
    });
});

// العناصر الخاصة بالوجه الأمامي
const frontUploadArea = document.getElementById('front-upload-area');
const frontPlaceholder = document.getElementById('front-placeholder');
const frontPreview = document.getElementById('front-preview');
const frontInput = document.getElementById('front-document');
const frontUploadBtn = document.getElementById('front-upload-btn');

// عناصر الوجه الخلفي
const backUploadArea = document.getElementById('back-upload-area');
const backPlaceholder = document.getElementById('back-placeholder');
const backPreview = document.getElementById('back-preview');
const backInput = document.getElementById('back-document');
const backUploadBtn = document.getElementById('back-upload-btn');

// زر الإرسال و checkbox
const consentCheckbox = document.getElementById('consent-checkbox');
const submitBtn = document.getElementById('submit-btn');

// رفع الصورة من نقر أو من زر اختيار الملف (الوجه الأمامي)
frontUploadArea.addEventListener('click', () => frontInput.click());
frontUploadBtn.addEventListener('click', () => frontInput.click());
frontInput.addEventListener('change', () => {
    handleFileUpload(frontInput, frontPreview, frontPlaceholder);
});

// رفع الصورة من نقر أو من زر اختيار الملف (الوجه الخلفي)
backUploadArea.addEventListener('click', () => backInput.click());
backUploadBtn.addEventListener('click', () => backInput.click());
backInput.addEventListener('change', () => {
    handleFileUpload(backInput, backPreview, backPlaceholder);
});

// دالة رفع الملف وعرض المعاينة
function handleFileUpload(input, previewElement, placeholderElement) {
    const file = input.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        previewElement.style.backgroundImage = `url(${e.target.result})`;
        previewElement.style.display = 'block';
        placeholderElement.style.display = 'none';
        checkFormValidity();
    };
    reader.readAsDataURL(file);
}

// دالة لإفراغ الملف ومعاينة الصورة (مثلاً عند اختيار جواز)
function clearFile(input, previewElement, placeholderElement) {
    input.value = '';
    previewElement.style.backgroundImage = '';
    previewElement.style.display = 'none';
    placeholderElement.style.display = 'flex'; // أو 'block' حسب CSS عندك
}

// التحقق من صحة النموذج لتمكين زر الإرسال


consentCheckbox.addEventListener('change', checkFormValidity);



// دعم Drag & Drop للوجهين (أمامي وخلفي)
['front', 'back'].forEach(side => {
    const dropArea = document.getElementById(`${side}-upload-area`);
    const input = document.getElementById(`${side}-document`);
    const preview = document.getElementById(`${side}-preview`);
    const placeholder = document.getElementById(`${side}-placeholder`);

    // منع السلوك الافتراضي للـ Drag & Drop
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, e => {
            e.preventDefault();
            e.stopPropagation();
        }, false);
    });

    // تمييز المنطقة عند السحب
    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, () => {
            dropArea.classList.add('highlight');
        }, false);
    });

    // إزالة التمييز عند ترك السحب أو الإسقاط
    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, () => {
            dropArea.classList.remove('highlight');
        }, false);
    });

    // التعامل مع الملف المسحوب
    dropArea.addEventListener('drop', e => {
        const dt = e.dataTransfer;
        const files = dt.files;
        if (files.length > 0) {
            input.files = files;
            handleFileUpload(input, preview, placeholder);
        }
    }, false);
});
